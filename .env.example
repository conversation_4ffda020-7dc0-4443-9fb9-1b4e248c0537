# AWS Management Assistant Configuration
# Copy this file to .env and update the values

# Core Configuration
AWS_AGENT_URL=http://localhost:8001
API_TIMEOUT=120
MAX_FILE_SIZE=10485760

# AWS Configuration
AWS_REGION=us-east-1
AWS_PROFILE=default

# Chainlit Configuration
CHAINLIT_HOST=0.0.0.0
CHAINLIT_PORT=8000
CHAINLIT_SESSION_TIMEOUT=3600

# Security Configuration (optional)
SECRET_KEY=your-secret-key-here
ENABLE_AUTH=false

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Development Configuration
DEBUG=false
ENVIRONMENT=development
