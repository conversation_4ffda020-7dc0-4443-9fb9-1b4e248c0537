#!/usr/bin/env python3
"""
Installation script for AWS Management Assistant dependencies.
This script installs dependencies in stages to avoid resolution conflicts.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_core_dependencies():
    """Install core dependencies first."""
    core_deps = [
        "pip>=23.0.0",
        "setuptools>=65.0.0",
        "wheel>=0.38.0"
    ]
    
    for dep in core_deps:
        if not run_command(f"pip install '{dep}'", f"Installing {dep}"):
            return False
    return True

def install_backend_dependencies():
    """Install existing backend dependencies."""
    print("\n📦 Installing backend dependencies...")
    return run_command("pip install -r requirements.txt", "Installing backend requirements")

def install_chainlit_dependencies():
    """Install Chainlit and essential frontend dependencies."""
    essential_deps = [
        "chainlit==1.0.504",
        "httpx==0.27.0", 
        "pandas==2.2.2",
        "numpy==1.26.4",
        "plotly==5.22.0",
        "python-dotenv==1.0.1"
    ]
    
    print("\n🎨 Installing Chainlit and essential frontend dependencies...")
    for dep in essential_deps:
        if not run_command(f"pip install '{dep}'", f"Installing {dep}"):
            return False
    return True

def install_optional_dependencies():
    """Install optional dependencies that might cause conflicts."""
    optional_deps = [
        "openpyxl==3.1.5",
        "xlsxwriter==3.1.9",
        "structlog==24.1.0",
        "prometheus-client==0.20.0"
    ]
    
    print("\n🔧 Installing optional dependencies...")
    success_count = 0
    
    for dep in optional_deps:
        if run_command(f"pip install '{dep}'", f"Installing {dep}"):
            success_count += 1
        else:
            print(f"⚠️  Optional dependency {dep} failed to install - continuing...")
    
    print(f"\n📊 Installed {success_count}/{len(optional_deps)} optional dependencies")
    return True

def verify_installation():
    """Verify that key packages can be imported."""
    packages_to_test = [
        ("chainlit", "Chainlit framework"),
        ("httpx", "HTTP client"),
        ("pandas", "Data processing"),
        ("plotly", "Visualization"),
        ("dotenv", "Environment variables")
    ]
    
    print("\n🔍 Verifying installation...")
    all_good = True
    
    for package, description in packages_to_test:
        try:
            __import__(package)
            print(f"✅ {description} - OK")
        except ImportError:
            print(f"❌ {description} - FAILED")
            all_good = False
    
    return all_good

def create_directories():
    """Create necessary directories."""
    directories = ["logs", "components"]
    
    print("\n📁 Creating necessary directories...")
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")

def main():
    """Main installation process."""
    print("🚀 AWS Management Assistant - Dependency Installation")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Install dependencies in stages
    steps = [
        (install_core_dependencies, "Installing core dependencies"),
        (install_backend_dependencies, "Installing backend dependencies"),
        (install_chainlit_dependencies, "Installing Chainlit dependencies"),
        (install_optional_dependencies, "Installing optional dependencies"),
        (verify_installation, "Verifying installation")
    ]
    
    for step_func, step_name in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        if not step_func():
            print(f"\n❌ Installation failed at: {step_name}")
            print("\n🔧 Troubleshooting tips:")
            print("1. Try upgrading pip: python -m pip install --upgrade pip")
            print("2. Clear pip cache: pip cache purge")
            print("3. Use virtual environment: python -m venv venv && venv\\Scripts\\activate")
            print("4. Install minimal version: pip install -r chainlit_requirements_minimal.txt")
            sys.exit(1)
    
    print("\n" + "="*60)
    print("🎉 Installation completed successfully!")
    print("\n📋 Next steps:")
    print("1. Configure environment: copy .env.example to .env")
    print("2. Start AWS Agent: python aws_agent.py")
    print("3. Start Chainlit: chainlit run chainlit_app.py")
    print("4. Open browser: http://localhost:8000")
    print("\n🆘 If you encounter issues:")
    print("- Check logs in the 'logs' directory")
    print("- Run: python -c 'import chainlit; print(chainlit.__version__)'")
    print("- Use minimal install: pip install -r chainlit_requirements_minimal.txt")

if __name__ == "__main__":
    main()
