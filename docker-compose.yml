version: '3.8'

services:
  aws-agent:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: aws-agent-backend
    ports:
      - "8001:8001"
    environment:
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      - ./logs:/app/logs
      - ./servers_config.json:/app/servers_config.json:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - aws-assistant-network

  chainlit-frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: chainlit-frontend
    ports:
      - "8000:8000"
    environment:
      - AWS_AGENT_URL=http://aws-agent:8001
      - API_TIMEOUT=${API_TIMEOUT:-120}
      - MAX_FILE_SIZE=${MAX_FILE_SIZE:-10485760}
      - CHAINLIT_HOST=0.0.0.0
      - CHAINLIT_PORT=8000
    volumes:
      - ./logs:/app/logs
      - ./.chainlit:/app/.chainlit:ro
    depends_on:
      aws-agent:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - aws-assistant-network

  nginx:
    image: nginx:alpine
    container_name: aws-assistant-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - chainlit-frontend
    restart: unless-stopped
    networks:
      - aws-assistant-network

  redis:
    image: redis:7-alpine
    container_name: aws-assistant-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    networks:
      - aws-assistant-network

  prometheus:
    image: prom/prometheus:latest
    container_name: aws-assistant-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - aws-assistant-network

  grafana:
    image: grafana/grafana:latest
    container_name: aws-assistant-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - aws-assistant-network

volumes:
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

networks:
  aws-assistant-network:
    driver: bridge
