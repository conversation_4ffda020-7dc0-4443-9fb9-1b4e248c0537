# AWS Management Assistant - Deployment Guide

This guide provides comprehensive instructions for deploying the AWS Management Assistant in various environments.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Chainlit      │    │   AWS Agent     │    │   MCP Servers   │
│   Frontend      │◄──►│   Backend       │◄──►│   (Pricing,     │
│   (Port 8000)   │    │   (Port 8001)   │    │   Cost, CFN)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Browser  │    │   AWS Services  │    │   External APIs │
│                 │    │   (EC2, S3,     │    │   (Pricing,     │
│                 │    │   RDS, Lambda)  │    │   Documentation)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start Deployment

### Prerequisites Checklist

- [ ] Python 3.8+ installed
- [ ] Node.js 16+ installed (for MCP servers)
- [ ] AWS CLI configured with appropriate credentials
- [ ] Git installed
- [ ] 4GB+ RAM available
- [ ] 10GB+ disk space

### 1. Environment Setup

```bash
# Clone the repository
git clone <repository-url>
cd MCP_Server

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r chainlit_requirements.txt
pip install -r requirements.txt
```

### 2. Configuration

Create `.env` file:

```env
# Core Configuration
AWS_AGENT_URL=http://localhost:8001
API_TIMEOUT=120
MAX_FILE_SIZE=10485760

# AWS Configuration
AWS_REGION=us-east-1
AWS_PROFILE=default

# Chainlit Configuration
CHAINLIT_HOST=0.0.0.0
CHAINLIT_PORT=8000
CHAINLIT_SESSION_TIMEOUT=3600

# Security Configuration
SECRET_KEY=your-secret-key-here
ENABLE_AUTH=false

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
```

### 3. Start Services

```bash
# Terminal 1: Start AWS Agent Backend
python aws_agent.py

# Terminal 2: Start Chainlit Frontend
chainlit run chainlit_app.py --host 0.0.0.0 --port 8000
```

### 4. Verify Deployment

- Frontend: http://localhost:8000
- Backend Health: http://localhost:8001/health
- Test basic functionality with a simple query

## 🐳 Docker Deployment

### Single Container Deployment

Create `Dockerfile`:

```dockerfile
FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    nodejs \
    npm \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY chainlit_requirements.txt requirements.txt ./
RUN pip install --no-cache-dir -r chainlit_requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create logs directory
RUN mkdir -p logs

# Expose ports
EXPOSE 8000 8001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Start both services
CMD ["bash", "-c", "python aws_agent.py & sleep 10 && chainlit run chainlit_app.py --host 0.0.0.0 --port 8000"]
```

Build and run:

```bash
# Build image
docker build -t aws-management-assistant .

# Run container
docker run -d \
  --name aws-assistant \
  -p 8000:8000 \
  -p 8001:8001 \
  -e AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID \
  -e AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY \
  -e AWS_REGION=$AWS_REGION \
  aws-management-assistant
```

### Multi-Container Deployment with Docker Compose

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  aws-agent:
    build:
      context: .
      dockerfile: Dockerfile.backend
    ports:
      - "8001:8001"
    environment:
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION}
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  chainlit-frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    ports:
      - "8000:8000"
    environment:
      - AWS_AGENT_URL=http://aws-agent:8001
    depends_on:
      aws-agent:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - chainlit-frontend
```

Deploy with:

```bash
docker-compose up -d
```

## ☁️ Cloud Deployment

### AWS ECS Deployment

1. **Create Task Definition**:

```json
{
  "family": "aws-management-assistant",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "aws-assistant",
      "image": "your-ecr-repo/aws-management-assistant:latest",
      "portMappings": [
        {"containerPort": 8000, "protocol": "tcp"},
        {"containerPort": 8001, "protocol": "tcp"}
      ],
      "environment": [
        {"name": "AWS_REGION", "value": "us-east-1"}
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/aws-management-assistant",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

2. **Create ECS Service**:

```bash
aws ecs create-service \
  --cluster your-cluster \
  --service-name aws-management-assistant \
  --task-definition aws-management-assistant:1 \
  --desired-count 2 \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[subnet-12345],securityGroups=[sg-12345],assignPublicIp=ENABLED}"
```

### Kubernetes Deployment

Create `k8s-deployment.yaml`:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: aws-management-assistant
spec:
  replicas: 2
  selector:
    matchLabels:
      app: aws-management-assistant
  template:
    metadata:
      labels:
        app: aws-management-assistant
    spec:
      containers:
      - name: aws-assistant
        image: aws-management-assistant:latest
        ports:
        - containerPort: 8000
        - containerPort: 8001
        env:
        - name: AWS_REGION
          value: "us-east-1"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: aws-management-assistant-service
spec:
  selector:
    app: aws-management-assistant
  ports:
  - name: frontend
    port: 8000
    targetPort: 8000
  - name: backend
    port: 8001
    targetPort: 8001
  type: LoadBalancer
```

Deploy:

```bash
kubectl apply -f k8s-deployment.yaml
```

## 🔧 Production Configuration

### Environment Variables

```env
# Production Environment
NODE_ENV=production
ENVIRONMENT=production

# Security
SECRET_KEY=your-production-secret-key
ENABLE_AUTH=true
SESSION_TIMEOUT=1800
CORS_ORIGINS=https://your-domain.com

# Database (if using persistent storage)
DATABASE_URL=********************************/dbname

# Monitoring
SENTRY_DSN=your-sentry-dsn
DATADOG_API_KEY=your-datadog-key

# AWS Configuration
AWS_REGION=us-east-1
AWS_ROLE_ARN=arn:aws:iam::ACCOUNT:role/ProductionRole

# Performance
WORKER_PROCESSES=4
MAX_CONNECTIONS=1000
CACHE_TTL=300

# Logging
LOG_LEVEL=WARNING
LOG_FORMAT=json
AUDIT_LOGGING=true
```

### Nginx Configuration

Create `nginx.conf`:

```nginx
upstream chainlit_backend {
    server 127.0.0.1:8000;
}

upstream aws_agent_backend {
    server 127.0.0.1:8001;
}

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=********; includeSubDomains";

    # Frontend
    location / {
        proxy_pass http://chainlit_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # Backend API
    location /api/ {
        proxy_pass http://aws_agent_backend/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # File uploads
    client_max_body_size 50M;
    
    # Logging
    access_log /var/log/nginx/aws-assistant-access.log;
    error_log /var/log/nginx/aws-assistant-error.log;
}
```

### Systemd Service

Create `/etc/systemd/system/aws-assistant.service`:

```ini
[Unit]
Description=AWS Management Assistant
After=network.target

[Service]
Type=forking
User=aws-assistant
Group=aws-assistant
WorkingDirectory=/opt/aws-assistant
Environment=PATH=/opt/aws-assistant/venv/bin
ExecStart=/opt/aws-assistant/start.sh
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=on-failure
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Create `/opt/aws-assistant/start.sh`:

```bash
#!/bin/bash
cd /opt/aws-assistant
source venv/bin/activate

# Start AWS Agent in background
python aws_agent.py &
AWS_AGENT_PID=$!

# Wait for backend to start
sleep 10

# Start Chainlit frontend
chainlit run chainlit_app.py --host 0.0.0.0 --port 8000 &
CHAINLIT_PID=$!

# Wait for any process to exit
wait $AWS_AGENT_PID $CHAINLIT_PID
```

Enable and start:

```bash
sudo systemctl enable aws-assistant
sudo systemctl start aws-assistant
sudo systemctl status aws-assistant
```

## 📊 Monitoring and Logging

### Health Checks

Implement comprehensive health checks:

```python
# Add to chainlit_app.py
@cl.on_settings_update
async def health_endpoint():
    """Health check endpoint for monitoring."""
    try:
        # Check backend connectivity
        health = await aws_client.health_check()
        
        # Check database connectivity (if applicable)
        # db_status = await check_database()
        
        # Check external dependencies
        # external_status = await check_external_services()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "backend": health,
            "version": "1.0.0"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
```

### Logging Configuration

```python
import logging
import logging.handlers

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.handlers.RotatingFileHandler(
            'logs/app.log',
            maxBytes=10485760,  # 10MB
            backupCount=5
        ),
        logging.StreamHandler()
    ]
)

# Audit logging
audit_logger = logging.getLogger('audit')
audit_handler = logging.handlers.RotatingFileHandler(
    'logs/audit.log',
    maxBytes=10485760,
    backupCount=10
)
audit_handler.setFormatter(
    logging.Formatter('%(asctime)s - %(message)s')
)
audit_logger.addHandler(audit_handler)
```

### Metrics Collection

Integrate with monitoring systems:

```python
# Prometheus metrics
from prometheus_client import Counter, Histogram, Gauge

request_count = Counter('requests_total', 'Total requests', ['method', 'endpoint'])
request_duration = Histogram('request_duration_seconds', 'Request duration')
active_sessions = Gauge('active_sessions', 'Number of active sessions')

# Usage in application
@request_duration.time()
async def process_request():
    request_count.labels(method='POST', endpoint='/chat').inc()
    # ... process request
```

## 🔒 Security Hardening

### Authentication Setup

```python
# Add authentication middleware
import jwt
from functools import wraps

def require_auth(f):
    @wraps(f)
    async def decorated_function(*args, **kwargs):
        token = cl.user_session.get("auth_token")
        if not token:
            raise cl.AuthError("Authentication required")
        
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            cl.user_session.set("user_id", payload['user_id'])
        except jwt.InvalidTokenError:
            raise cl.AuthError("Invalid token")
        
        return await f(*args, **kwargs)
    return decorated_function
```

### Input Validation

```python
from pydantic import BaseModel, validator

class ChatRequest(BaseModel):
    message: str
    session_id: str
    
    @validator('message')
    def validate_message(cls, v):
        if len(v) > 10000:
            raise ValueError('Message too long')
        if not v.strip():
            raise ValueError('Message cannot be empty')
        return v.strip()
```

### Rate Limiting

```python
from collections import defaultdict
from datetime import datetime, timedelta

class RateLimiter:
    def __init__(self, max_requests=100, window_minutes=1):
        self.max_requests = max_requests
        self.window = timedelta(minutes=window_minutes)
        self.requests = defaultdict(list)
    
    def is_allowed(self, user_id: str) -> bool:
        now = datetime.now()
        user_requests = self.requests[user_id]
        
        # Remove old requests
        user_requests[:] = [req for req in user_requests if now - req < self.window]
        
        if len(user_requests) >= self.max_requests:
            return False
        
        user_requests.append(now)
        return True
```

## 🚨 Troubleshooting

### Common Deployment Issues

1. **Port Conflicts**
   ```bash
   # Check port usage
   netstat -tulpn | grep :8000
   netstat -tulpn | grep :8001
   
   # Kill processes if needed
   sudo fuser -k 8000/tcp
   sudo fuser -k 8001/tcp
   ```

2. **Permission Issues**
   ```bash
   # Fix file permissions
   chmod +x start.sh
   chown -R aws-assistant:aws-assistant /opt/aws-assistant
   ```

3. **Memory Issues**
   ```bash
   # Monitor memory usage
   free -h
   ps aux --sort=-%mem | head
   
   # Adjust container limits
   docker run --memory=2g --memory-swap=4g ...
   ```

4. **Network Connectivity**
   ```bash
   # Test connectivity
   curl -f http://localhost:8001/health
   telnet localhost 8000
   ```

### Log Analysis

```bash
# View application logs
tail -f logs/app.log

# Search for errors
grep -i error logs/app.log | tail -20

# Monitor real-time logs
journalctl -u aws-assistant -f

# Docker logs
docker logs -f aws-assistant
```

### Performance Tuning

1. **Optimize Python Settings**
   ```bash
   export PYTHONOPTIMIZE=1
   export PYTHONDONTWRITEBYTECODE=1
   ```

2. **Tune Chainlit Settings**
   ```python
   # In chainlit_app.py
   cl.config.max_message_size = 1000000
   cl.config.session_timeout = 3600
   ```

3. **Database Optimization** (if using)
   ```sql
   -- Add indexes for common queries
   CREATE INDEX idx_sessions_user_id ON sessions(user_id);
   CREATE INDEX idx_messages_timestamp ON messages(timestamp);
   ```

## 📈 Scaling Considerations

### Horizontal Scaling

1. **Load Balancer Configuration**
2. **Session Affinity** (sticky sessions)
3. **Shared State Management** (Redis/Database)
4. **Auto-scaling Policies**

### Vertical Scaling

1. **Resource Monitoring**
2. **Performance Profiling**
3. **Memory Optimization**
4. **CPU Utilization**

---

This deployment guide provides comprehensive instructions for various deployment scenarios. Choose the approach that best fits your infrastructure and requirements.
