"""
AWS Data Visualization Components for Chainlit

This module provides visualization components for AWS data including
cost charts, resource dashboards, and performance metrics.
"""

import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json

class AWSVisualizationManager:
    """Manager for creating AWS-related visualizations."""
    
    def __init__(self):
        self.color_palette = {
            'primary': '#FF9900',  # AWS Orange
            'secondary': '#232F3E',  # AWS Dark Blue
            'success': '#4CAF50',
            'warning': '#FF9800',
            'error': '#F44336',
            'info': '#2196F3'
        }
    
    def create_cost_trend_chart(self, cost_data: List[Dict]) -> go.Figure:
        """Create a cost trend chart over time."""
        if not cost_data:
            return self._create_empty_chart("No cost data available")
        
        df = pd.DataFrame(cost_data)
        
        fig = go.Figure()
        
        # Add cost trend line
        fig.add_trace(go.Scatter(
            x=df['date'],
            y=df['cost'],
            mode='lines+markers',
            name='Daily Cost',
            line=dict(color=self.color_palette['primary'], width=3),
            marker=dict(size=8)
        ))
        
        # Add moving average if enough data points
        if len(df) >= 7:
            df['ma_7'] = df['cost'].rolling(window=7).mean()
            fig.add_trace(go.Scatter(
                x=df['date'],
                y=df['ma_7'],
                mode='lines',
                name='7-day Average',
                line=dict(color=self.color_palette['info'], width=2, dash='dash')
            ))
        
        fig.update_layout(
            title='AWS Cost Trends',
            xaxis_title='Date',
            yaxis_title='Cost ($)',
            template='plotly_white',
            height=400
        )
        
        return fig
    
    def create_service_cost_breakdown(self, service_costs: Dict[str, float]) -> go.Figure:
        """Create a pie chart for service cost breakdown."""
        if not service_costs:
            return self._create_empty_chart("No service cost data available")
        
        services = list(service_costs.keys())
        costs = list(service_costs.values())
        
        fig = go.Figure(data=[go.Pie(
            labels=services,
            values=costs,
            hole=0.4,
            textinfo='label+percent',
            textposition='outside'
        )])
        
        fig.update_layout(
            title='Cost Breakdown by Service',
            template='plotly_white',
            height=400
        )
        
        return fig
    
    def create_resource_utilization_chart(self, utilization_data: List[Dict]) -> go.Figure:
        """Create a resource utilization chart."""
        if not utilization_data:
            return self._create_empty_chart("No utilization data available")
        
        df = pd.DataFrame(utilization_data)
        
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('CPU Utilization', 'Memory Utilization', 
                          'Network I/O', 'Disk I/O'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # CPU Utilization
        fig.add_trace(
            go.Scatter(x=df['timestamp'], y=df['cpu_percent'], 
                      name='CPU %', line=dict(color=self.color_palette['primary'])),
            row=1, col=1
        )
        
        # Memory Utilization
        fig.add_trace(
            go.Scatter(x=df['timestamp'], y=df['memory_percent'], 
                      name='Memory %', line=dict(color=self.color_palette['info'])),
            row=1, col=2
        )
        
        # Network I/O
        fig.add_trace(
            go.Scatter(x=df['timestamp'], y=df['network_in'], 
                      name='Network In', line=dict(color=self.color_palette['success'])),
            row=2, col=1
        )
        
        # Disk I/O
        fig.add_trace(
            go.Scatter(x=df['timestamp'], y=df['disk_read'], 
                      name='Disk Read', line=dict(color=self.color_palette['warning'])),
            row=2, col=2
        )
        
        fig.update_layout(
            title='Resource Utilization Metrics',
            template='plotly_white',
            height=600,
            showlegend=False
        )
        
        return fig
    
    def create_cost_optimization_chart(self, optimization_data: List[Dict]) -> go.Figure:
        """Create a chart showing cost optimization opportunities."""
        if not optimization_data:
            return self._create_empty_chart("No optimization data available")
        
        df = pd.DataFrame(optimization_data)
        
        fig = go.Figure()
        
        # Current costs
        fig.add_trace(go.Bar(
            name='Current Cost',
            x=df['resource'],
            y=df['current_cost'],
            marker_color=self.color_palette['error']
        ))
        
        # Optimized costs
        fig.add_trace(go.Bar(
            name='Optimized Cost',
            x=df['resource'],
            y=df['optimized_cost'],
            marker_color=self.color_palette['success']
        ))
        
        # Add savings annotations
        for i, row in df.iterrows():
            savings = row['current_cost'] - row['optimized_cost']
            savings_percent = (savings / row['current_cost']) * 100
            
            fig.add_annotation(
                x=row['resource'],
                y=max(row['current_cost'], row['optimized_cost']) + 10,
                text=f"Save ${savings:.2f}<br>({savings_percent:.1f}%)",
                showarrow=True,
                arrowhead=2,
                arrowcolor=self.color_palette['success']
            )
        
        fig.update_layout(
            title='Cost Optimization Opportunities',
            xaxis_title='Resources',
            yaxis_title='Monthly Cost ($)',
            barmode='group',
            template='plotly_white',
            height=500
        )
        
        return fig
    
    def create_regional_cost_comparison(self, regional_data: Dict[str, Dict]) -> go.Figure:
        """Create a regional cost comparison chart."""
        if not regional_data:
            return self._create_empty_chart("No regional data available")
        
        regions = list(regional_data.keys())
        services = list(next(iter(regional_data.values())).keys())
        
        fig = go.Figure()
        
        for service in services:
            costs = [regional_data[region].get(service, 0) for region in regions]
            fig.add_trace(go.Bar(
                name=service,
                x=regions,
                y=costs
            ))
        
        fig.update_layout(
            title='Cost Comparison by Region',
            xaxis_title='AWS Regions',
            yaxis_title='Monthly Cost ($)',
            barmode='group',
            template='plotly_white',
            height=400
        )
        
        return fig
    
    def create_performance_dashboard(self, performance_data: Dict) -> go.Figure:
        """Create a comprehensive performance dashboard."""
        if not performance_data:
            return self._create_empty_chart("No performance data available")
        
        fig = make_subplots(
            rows=2, cols=3,
            subplot_titles=('Response Time', 'Throughput', 'Error Rate',
                          'Resource Health', 'Cost Efficiency', 'Availability'),
            specs=[[{"type": "scatter"}, {"type": "bar"}, {"type": "indicator"}],
                   [{"type": "indicator"}, {"type": "scatter"}, {"type": "indicator"}]]
        )
        
        # Response Time
        if 'response_times' in performance_data:
            rt_data = performance_data['response_times']
            fig.add_trace(
                go.Scatter(x=rt_data['timestamps'], y=rt_data['values'],
                          mode='lines', name='Response Time'),
                row=1, col=1
            )
        
        # Throughput
        if 'throughput' in performance_data:
            tp_data = performance_data['throughput']
            fig.add_trace(
                go.Bar(x=tp_data['services'], y=tp_data['requests_per_second']),
                row=1, col=2
            )
        
        # Error Rate Gauge
        error_rate = performance_data.get('error_rate', 0)
        fig.add_trace(
            go.Indicator(
                mode="gauge+number",
                value=error_rate,
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "Error Rate (%)"},
                gauge={'axis': {'range': [None, 10]},
                      'bar': {'color': self._get_gauge_color(error_rate, [2, 5])},
                      'steps': [{'range': [0, 2], 'color': "lightgray"},
                               {'range': [2, 5], 'color': "yellow"}],
                      'threshold': {'line': {'color': "red", 'width': 4},
                                   'thickness': 0.75, 'value': 5}}
            ),
            row=1, col=3
        )
        
        fig.update_layout(
            title='AWS Performance Dashboard',
            template='plotly_white',
            height=800
        )
        
        return fig
    
    def _create_empty_chart(self, message: str) -> go.Figure:
        """Create an empty chart with a message."""
        fig = go.Figure()
        fig.add_annotation(
            x=0.5, y=0.5,
            text=message,
            showarrow=False,
            font=dict(size=16, color=self.color_palette['secondary'])
        )
        fig.update_layout(
            template='plotly_white',
            height=300,
            xaxis={'visible': False},
            yaxis={'visible': False}
        )
        return fig
    
    def _get_gauge_color(self, value: float, thresholds: List[float]) -> str:
        """Get color for gauge based on value and thresholds."""
        if value <= thresholds[0]:
            return self.color_palette['success']
        elif value <= thresholds[1]:
            return self.color_palette['warning']
        else:
            return self.color_palette['error']

# Utility functions for data formatting
def format_cost_data(raw_data: List[Dict]) -> List[Dict]:
    """Format raw cost data for visualization."""
    formatted_data = []
    for item in raw_data:
        formatted_data.append({
            'date': pd.to_datetime(item.get('date', datetime.now())),
            'cost': float(item.get('cost', 0)),
            'service': item.get('service', 'Unknown')
        })
    return formatted_data

def generate_sample_data() -> Dict:
    """Generate sample data for testing visualizations."""
    # Generate sample cost data
    dates = pd.date_range(start='2024-01-01', end='2024-01-31', freq='D')
    cost_data = []
    
    for date in dates:
        cost_data.append({
            'date': date.strftime('%Y-%m-%d'),
            'cost': np.random.uniform(100, 500)
        })
    
    service_costs = {
        'EC2': 1250.50,
        'S3': 89.25,
        'RDS': 456.75,
        'Lambda': 23.10,
        'CloudWatch': 45.30
    }
    
    return {
        'cost_trends': cost_data,
        'service_costs': service_costs,
        'utilization_data': [],
        'optimization_data': [],
        'regional_data': {},
        'performance_data': {}
    }
