"""
File Upload and Export Operations for AWS Management

This module handles file upload capabilities for AWS configurations/scripts
and export functionality for reports and data.
"""

import os
import json
import csv
import yaml
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import tempfile
import zipfile
import io
import base64
from dataclasses import dataclass
import chainlit as cl

@dataclass
class FileAnalysis:
    """Results of file analysis."""
    file_type: str
    size_bytes: int
    line_count: int
    aws_resources: List[str]
    security_issues: List[str]
    recommendations: List[str]
    metadata: Dict[str, Any]

class FileProcessor:
    """Handles processing of uploaded files."""
    
    def __init__(self):
        self.supported_extensions = {
            '.json': 'JSON Configuration',
            '.yaml': 'YAML Configuration',
            '.yml': 'YAML Configuration',
            '.py': 'Python Script',
            '.sh': 'Shell Script',
            '.ps1': 'PowerShell Script',
            '.csv': 'CSV Data',
            '.xlsx': 'Excel Data',
            '.txt': 'Text File',
            '.md': 'Markdown Documentation',
            '.tf': 'Terraform Configuration',
            '.cfn': 'CloudFormation Template'
        }
        
        self.max_file_size = 10 * 1024 * 1024  # 10MB
    
    async def process_file(self, file: cl.File) -> FileAnalysis:
        """Process uploaded file and return analysis."""
        try:
            # Validate file
            self._validate_file(file)
            
            # Determine file type
            file_extension = Path(file.name).suffix.lower()
            file_type = self.supported_extensions.get(file_extension, 'Unknown')
            
            # Read file content
            content = file.content.decode('utf-8', errors='ignore')
            
            # Analyze content
            analysis = await self._analyze_content(content, file_type, file.name)
            
            # Create file analysis result
            return FileAnalysis(
                file_type=file_type,
                size_bytes=file.size,
                line_count=len(content.split('\n')),
                aws_resources=analysis.get('aws_resources', []),
                security_issues=analysis.get('security_issues', []),
                recommendations=analysis.get('recommendations', []),
                metadata=analysis.get('metadata', {})
            )
            
        except Exception as e:
            raise Exception(f"Failed to process file: {str(e)}")
    
    def _validate_file(self, file: cl.File):
        """Validate uploaded file."""
        if file.size > self.max_file_size:
            raise ValueError(f"File too large. Maximum size is {self.max_file_size / 1024 / 1024:.1f}MB")
        
        file_extension = Path(file.name).suffix.lower()
        if file_extension not in self.supported_extensions:
            raise ValueError(f"Unsupported file type: {file_extension}")
    
    async def _analyze_content(self, content: str, file_type: str, filename: str) -> Dict[str, Any]:
        """Analyze file content for AWS resources and security issues."""
        analysis = {
            'aws_resources': [],
            'security_issues': [],
            'recommendations': [],
            'metadata': {}
        }
        
        # AWS resource detection
        aws_services = [
            'ec2', 's3', 'rds', 'lambda', 'cloudformation', 'iam', 'vpc',
            'elb', 'cloudwatch', 'sns', 'sqs', 'dynamodb', 'elasticache'
        ]
        
        content_lower = content.lower()
        for service in aws_services:
            if service in content_lower:
                analysis['aws_resources'].append(service.upper())
        
        # Security analysis
        security_patterns = [
            ('hardcoded_key', ['aws_access_key_id', 'aws_secret_access_key']),
            ('public_access', ['0.0.0.0/0', '*']),
            ('insecure_protocol', ['http://', 'ftp://']),
            ('weak_encryption', ['md5', 'sha1'])
        ]
        
        for issue_type, patterns in security_patterns:
            for pattern in patterns:
                if pattern in content_lower:
                    analysis['security_issues'].append({
                        'type': issue_type,
                        'pattern': pattern,
                        'severity': 'high' if issue_type in ['hardcoded_key', 'public_access'] else 'medium'
                    })
        
        # File-specific analysis
        if file_type in ['JSON Configuration', 'YAML Configuration']:
            analysis.update(await self._analyze_config_file(content, filename))
        elif file_type in ['Python Script', 'Shell Script', 'PowerShell Script']:
            analysis.update(await self._analyze_script_file(content, filename))
        elif file_type == 'Terraform Configuration':
            analysis.update(await self._analyze_terraform_file(content, filename))
        elif file_type in ['CSV Data', 'Excel Data']:
            analysis.update(await self._analyze_data_file(content, filename))
        
        return analysis
    
    async def _analyze_config_file(self, content: str, filename: str) -> Dict[str, Any]:
        """Analyze configuration files (JSON/YAML)."""
        analysis = {'recommendations': []}
        
        try:
            if filename.endswith('.json'):
                config = json.loads(content)
            else:
                config = yaml.safe_load(content)
            
            # CloudFormation template analysis
            if 'Resources' in config:
                analysis['metadata']['template_type'] = 'CloudFormation'
                analysis['metadata']['resource_count'] = len(config['Resources'])
                
                # Check for best practices
                if 'Parameters' not in config:
                    analysis['recommendations'].append('Consider adding Parameters for flexibility')
                
                if 'Outputs' not in config:
                    analysis['recommendations'].append('Consider adding Outputs for resource references')
            
            # MCP server configuration
            elif 'mcpServers' in config:
                analysis['metadata']['config_type'] = 'MCP Servers'
                analysis['metadata']['server_count'] = len(config['mcpServers'])
            
        except Exception as e:
            analysis['recommendations'].append(f'Configuration parsing failed: {str(e)}')
        
        return analysis
    
    async def _analyze_script_file(self, content: str, filename: str) -> Dict[str, Any]:
        """Analyze script files."""
        analysis = {'recommendations': []}
        
        lines = content.split('\n')
        analysis['metadata']['line_count'] = len(lines)
        
        # Check for AWS CLI usage
        aws_cli_commands = [line for line in lines if 'aws ' in line.lower()]
        analysis['metadata']['aws_cli_commands'] = len(aws_cli_commands)
        
        # Check for error handling
        has_error_handling = any(keyword in content.lower() for keyword in ['try:', 'except:', 'catch', 'trap'])
        if not has_error_handling:
            analysis['recommendations'].append('Add error handling for robustness')
        
        # Check for logging
        has_logging = any(keyword in content.lower() for keyword in ['log', 'echo', 'print'])
        if not has_logging:
            analysis['recommendations'].append('Add logging for better debugging')
        
        return analysis
    
    async def _analyze_terraform_file(self, content: str, filename: str) -> Dict[str, Any]:
        """Analyze Terraform configuration files."""
        analysis = {'recommendations': []}
        
        # Count resource blocks
        resource_count = content.count('resource "')
        analysis['metadata']['resource_count'] = resource_count
        
        # Check for variables
        has_variables = 'variable "' in content
        if not has_variables and resource_count > 0:
            analysis['recommendations'].append('Consider using variables for flexibility')
        
        # Check for outputs
        has_outputs = 'output "' in content
        if not has_outputs and resource_count > 0:
            analysis['recommendations'].append('Consider adding outputs for resource references')
        
        return analysis
    
    async def _analyze_data_file(self, content: str, filename: str) -> Dict[str, Any]:
        """Analyze data files (CSV)."""
        analysis = {'recommendations': []}
        
        try:
            # Parse CSV content
            lines = content.split('\n')
            if len(lines) > 1:
                headers = lines[0].split(',')
                analysis['metadata']['columns'] = len(headers)
                analysis['metadata']['rows'] = len(lines) - 1
                analysis['metadata']['headers'] = [h.strip() for h in headers]
                
                # Check for AWS cost data patterns
                aws_cost_headers = ['service', 'cost', 'usage', 'region', 'resource']
                if any(header.lower() in [h.lower() for h in headers] for header in aws_cost_headers):
                    analysis['metadata']['data_type'] = 'AWS Cost Data'
                    analysis['recommendations'].append('This appears to be AWS cost data - consider using cost analysis tools')
        
        except Exception as e:
            analysis['recommendations'].append(f'Data parsing failed: {str(e)}')
        
        return analysis

class ReportExporter:
    """Handles exporting of reports and data."""
    
    def __init__(self):
        self.export_formats = ['json', 'csv', 'xlsx', 'pdf', 'html']
    
    async def export_cost_report(self, cost_data: Dict[str, Any], format: str = 'json') -> bytes:
        """Export cost analysis report."""
        if format == 'json':
            return self._export_json(cost_data)
        elif format == 'csv':
            return self._export_csv(cost_data)
        elif format == 'xlsx':
            return self._export_excel(cost_data)
        elif format == 'html':
            return self._export_html_report(cost_data, 'Cost Analysis Report')
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    async def export_infrastructure_report(self, infra_data: Dict[str, Any], format: str = 'json') -> bytes:
        """Export infrastructure overview report."""
        if format == 'json':
            return self._export_json(infra_data)
        elif format == 'html':
            return self._export_html_report(infra_data, 'Infrastructure Report')
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    async def export_security_report(self, security_data: Dict[str, Any], format: str = 'json') -> bytes:
        """Export security analysis report."""
        if format == 'json':
            return self._export_json(security_data)
        elif format == 'html':
            return self._export_html_report(security_data, 'Security Report')
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    async def create_backup_archive(self, data: Dict[str, Any]) -> bytes:
        """Create a backup archive with all data."""
        buffer = io.BytesIO()
        
        with zipfile.ZipFile(buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            # Add JSON data
            zip_file.writestr('data.json', json.dumps(data, indent=2))
            
            # Add CSV exports if applicable
            if 'cost_data' in data:
                csv_data = self._export_csv(data['cost_data'])
                zip_file.writestr('cost_report.csv', csv_data)
            
            # Add metadata
            metadata = {
                'export_date': datetime.now().isoformat(),
                'version': '1.0',
                'description': 'AWS Management Data Export'
            }
            zip_file.writestr('metadata.json', json.dumps(metadata, indent=2))
        
        buffer.seek(0)
        return buffer.getvalue()
    
    def _export_json(self, data: Dict[str, Any]) -> bytes:
        """Export data as JSON."""
        json_str = json.dumps(data, indent=2, default=str)
        return json_str.encode('utf-8')
    
    def _export_csv(self, data: Dict[str, Any]) -> bytes:
        """Export data as CSV."""
        output = io.StringIO()
        
        # Handle different data structures
        if 'service_breakdown' in data:
            # Cost data
            writer = csv.writer(output)
            writer.writerow(['Service', 'Cost'])
            for service, cost in data['service_breakdown'].items():
                writer.writerow([service, cost])
        elif isinstance(data, list):
            # List of dictionaries
            if data:
                writer = csv.DictWriter(output, fieldnames=data[0].keys())
                writer.writeheader()
                writer.writerows(data)
        else:
            # Generic key-value pairs
            writer = csv.writer(output)
            writer.writerow(['Key', 'Value'])
            for key, value in data.items():
                writer.writerow([key, str(value)])
        
        csv_content = output.getvalue()
        output.close()
        return csv_content.encode('utf-8')
    
    def _export_excel(self, data: Dict[str, Any]) -> bytes:
        """Export data as Excel."""
        output = io.BytesIO()
        
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            # Cost breakdown sheet
            if 'service_breakdown' in data:
                df = pd.DataFrame(list(data['service_breakdown'].items()), 
                                columns=['Service', 'Cost'])
                df.to_excel(writer, sheet_name='Service Costs', index=False)
            
            # Regional breakdown sheet
            if 'regional_breakdown' in data:
                df = pd.DataFrame(list(data['regional_breakdown'].items()), 
                                columns=['Region', 'Cost'])
                df.to_excel(writer, sheet_name='Regional Costs', index=False)
            
            # Summary sheet
            summary_data = {k: v for k, v in data.items() 
                          if not isinstance(v, (dict, list))}
            if summary_data:
                df = pd.DataFrame(list(summary_data.items()), 
                                columns=['Metric', 'Value'])
                df.to_excel(writer, sheet_name='Summary', index=False)
        
        output.seek(0)
        return output.getvalue()
    
    def _export_html_report(self, data: Dict[str, Any], title: str) -> bytes:
        """Export data as HTML report."""
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>{title}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .section {{ margin: 20px 0; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .metric {{ background-color: #e8f4fd; padding: 10px; margin: 5px 0; border-radius: 3px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>{title}</h1>
        <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="section">
        <h2>Summary</h2>
        {self._generate_html_summary(data)}
    </div>
    
    <div class="section">
        <h2>Detailed Data</h2>
        {self._generate_html_tables(data)}
    </div>
</body>
</html>
"""
        return html_content.encode('utf-8')
    
    def _generate_html_summary(self, data: Dict[str, Any]) -> str:
        """Generate HTML summary section."""
        html = ""
        
        # Key metrics
        key_metrics = {k: v for k, v in data.items() 
                      if not isinstance(v, (dict, list)) and k != 'timestamp'}
        
        for key, value in key_metrics.items():
            html += f'<div class="metric"><strong>{key.replace("_", " ").title()}:</strong> {value}</div>'
        
        return html
    
    def _generate_html_tables(self, data: Dict[str, Any]) -> str:
        """Generate HTML tables for data."""
        html = ""
        
        for key, value in data.items():
            if isinstance(value, dict) and value:
                html += f"<h3>{key.replace('_', ' ').title()}</h3>"
                html += "<table>"
                html += "<tr><th>Item</th><th>Value</th></tr>"
                for item, val in value.items():
                    html += f"<tr><td>{item}</td><td>{val}</td></tr>"
                html += "</table>"
            elif isinstance(value, list) and value:
                html += f"<h3>{key.replace('_', ' ').title()}</h3>"
                html += "<ul>"
                for item in value:
                    html += f"<li>{item}</li>"
                html += "</ul>"
        
        return html

# Utility functions
def get_file_icon(filename: str) -> str:
    """Get emoji icon for file type."""
    extension = Path(filename).suffix.lower()
    
    icons = {
        '.json': '📄',
        '.yaml': '📄',
        '.yml': '📄',
        '.py': '🐍',
        '.sh': '📜',
        '.ps1': '💻',
        '.csv': '📊',
        '.xlsx': '📊',
        '.txt': '📝',
        '.md': '📖',
        '.tf': '🏗️',
        '.cfn': '☁️'
    }
    
    return icons.get(extension, '📎')

def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format."""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"
