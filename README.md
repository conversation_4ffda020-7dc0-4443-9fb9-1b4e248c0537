# AWS Management Assistant - Chainlit Frontend

A comprehensive frontend application using Chainlit for AWS management operations with real-time chat interface, monitoring dashboards, and resource visualization capabilities.

## 🚀 Features

### Core Interface
- **Interactive Chat Interface**: Natural language communication with AWS agent
- **Real-time Message Streaming**: Live response handling and updates
- **User Authentication**: Session management and user context retention
- **Professional UI Design**: Clean, responsive design for AWS operations

### AWS Agent Integration
- **Comprehensive AWS Operations**: Support for EC2, S3, RDS, Lambda, and more
- **Structured Resource Display**: Tables, cards, and formatted responses
- **Error Handling**: Intelligent error management with recovery suggestions
- **Tool Integration**: Seamless integration with existing AWS agent backend

### Monitoring & Visualization
- **Real-time Dashboards**: AWS resource monitoring and metrics
- **Cost Analysis**: Comprehensive billing information and optimization
- **Infrastructure Health**: Status indicators and performance metrics
- **Interactive Charts**: Plotly-powered visualizations and graphs

### Advanced Features
- **File Upload**: Support for AWS configurations, scripts, and data files
- **Export Functionality**: Reports in JSON, CSV, Excel, and HTML formats
- **Search Capabilities**: Intelligent search across resources and documentation
- **Command Suggestions**: Context-aware command recommendations
- **Navigation System**: Breadcrumbs and contextual navigation

## 📋 Prerequisites

- Python 3.8 or higher
- Node.js 16+ (for MCP servers)
- AWS credentials configured
- Existing AWS Agent backend running

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd MCP_Server
```

### 2. Install Dependencies
```bash
# Install Python dependencies
pip install -r chainlit_requirements.txt

# Install existing backend dependencies
pip install -r requirements.txt
```

### 3. Environment Configuration
Create a `.env` file in the project root:

```env
# AWS Agent Configuration
AWS_AGENT_URL=http://localhost:8001
API_TIMEOUT=120

# File Upload Configuration
MAX_FILE_SIZE=10485760

# Chainlit Configuration
CHAINLIT_HOST=0.0.0.0
CHAINLIT_PORT=8000

# AWS Configuration (if needed)
AWS_REGION=us-east-1
AWS_PROFILE=default
```

### 4. Start the AWS Agent Backend
```bash
# Start the existing AWS agent
python aws_agent.py
```

### 5. Launch the Chainlit Frontend
```bash
# Start the Chainlit application
chainlit run chainlit_app.py --host 0.0.0.0 --port 8000
```

## 🎯 Usage Guide

### Getting Started

1. **Access the Application**: Open your browser to `http://localhost:8000`
2. **Start Chatting**: Ask natural language questions about your AWS resources
3. **Use Quick Actions**: Click dashboard buttons for common operations
4. **Upload Files**: Drag and drop AWS configuration files for analysis
5. **Export Reports**: Generate and download various report formats

### Example Commands

#### Cost Analysis
```
"Analyze my AWS costs for the last 30 days"
"Show me the most expensive services"
"Compare costs between us-east-1 and us-west-2"
"Generate a cost optimization report"
```

#### Resource Management
```
"List all EC2 instances in production"
"Show me S3 buckets with public access"
"Get details for RDS instance prod-db-01"
"Find unused Lambda functions"
```

#### Monitoring & Health
```
"Show infrastructure dashboard"
"Check system health status"
"Display performance metrics"
"Run security scan"
```

#### File Operations
```
Upload CloudFormation templates for analysis
Import cost data CSV files
Export infrastructure reports
Generate backup archives
```

### Special Commands

The application supports special commands starting with `/`:

- `/help` - Show comprehensive help documentation
- `/search <query>` - Search resources and documentation
- `/history` - View conversation history
- `/suggestions` - Show available command suggestions
- `/navigation` - Display navigation options
- `/clear` - Clear conversation history

## 📊 Dashboard Features

### Infrastructure Overview
- Real-time resource counts and status
- Service health indicators
- Recent alerts and notifications
- Quick access to detailed views

### Cost Analysis Dashboard
- Monthly spending breakdown
- Service and regional cost distribution
- Cost trends and forecasting
- Optimization opportunities

### Performance Metrics
- Resource utilization statistics
- Response time and throughput metrics
- Error rates and availability
- Performance recommendations

### Security Dashboard
- Security findings and compliance status
- Access review and permission audits
- Vulnerability assessments
- Security recommendations

## 🔧 Configuration

### Customizing the Interface

Edit `chainlit_app.py` to customize:
- Welcome messages and branding
- Available quick actions
- Dashboard layouts
- Color schemes and styling

### Adding New Features

The modular architecture allows easy extension:

1. **Components**: Add new modules in the `components/` directory
2. **Visualizations**: Extend `aws_visualizations.py` for new chart types
3. **Error Handling**: Customize error patterns in `error_handling.py`
4. **User Experience**: Enhance UX features in `user_experience.py`

### Backend Integration

To integrate with different AWS agent backends:

1. Update `AWSAgentClient` in `aws_agent_integration.py`
2. Modify API endpoints and request formats
3. Adjust response parsing logic
4. Update error handling patterns

## 📁 Project Structure

```
MCP_Server/
├── chainlit_app.py              # Main Chainlit application
├── chainlit_requirements.txt    # Frontend dependencies
├── components/                  # Modular components
│   ├── aws_agent_integration.py # Backend integration
│   ├── aws_visualizations.py    # Chart and graph components
│   ├── aws_resource_display.py  # Resource formatting
│   ├── monitoring_dashboard.py  # Dashboard implementations
│   ├── file_operations.py       # File upload/export
│   ├── user_experience.py       # UX enhancements
│   └── error_handling.py        # Error management
├── aws_agent.py                 # Existing AWS agent backend
├── requirements.txt             # Backend dependencies
├── servers_config.json          # MCP server configuration
└── README.md                    # This file
```

## 🚀 Deployment

### Development Deployment

For development environments:

```bash
# Start backend
python aws_agent.py &

# Start frontend
chainlit run chainlit_app.py --host 0.0.0.0 --port 8000
```

### Production Deployment

For production environments, consider:

1. **Process Management**: Use PM2, systemd, or Docker
2. **Reverse Proxy**: Configure nginx or Apache
3. **SSL/TLS**: Enable HTTPS with certificates
4. **Environment Variables**: Use secure configuration management
5. **Monitoring**: Implement logging and health checks

### Docker Deployment

Create a `Dockerfile`:

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY . .

RUN pip install -r chainlit_requirements.txt
RUN pip install -r requirements.txt

EXPOSE 8000 8001

CMD ["bash", "-c", "python aws_agent.py & chainlit run chainlit_app.py --host 0.0.0.0 --port 8000"]
```

Build and run:

```bash
docker build -t aws-management-assistant .
docker run -p 8000:8000 -p 8001:8001 aws-management-assistant
```

## 🔒 Security Considerations

- **Authentication**: Implement proper user authentication
- **Authorization**: Ensure appropriate AWS permissions
- **Data Protection**: Encrypt sensitive data in transit and at rest
- **Input Validation**: Validate all user inputs and file uploads
- **Session Management**: Implement secure session handling
- **Audit Logging**: Log all user actions and system events

## 🐛 Troubleshooting

### Common Issues

1. **Backend Connection Failed**
   - Verify AWS agent is running on correct port
   - Check firewall and network connectivity
   - Validate environment configuration

2. **File Upload Errors**
   - Check file size limits
   - Verify file format support
   - Ensure proper permissions

3. **Dashboard Loading Issues**
   - Verify AWS credentials and permissions
   - Check backend service health
   - Review error logs for details

4. **Performance Issues**
   - Monitor resource usage
   - Check network latency
   - Optimize query complexity

### Debug Mode

Enable debug logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Health Checks

Use the built-in health check endpoints:
- Frontend: `http://localhost:8000/health` (if implemented)
- Backend: `http://localhost:8001/health`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the troubleshooting section
- Review the documentation
- Open an issue on GitHub
- Contact the development team

---

**Ready to optimize your AWS infrastructure with AI-powered assistance!** 🚀
