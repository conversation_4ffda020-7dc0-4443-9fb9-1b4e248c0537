"""
User Experience Enhancement Components

This module provides navigation, command suggestions, help documentation,
search functionality, and interaction history features.
"""

import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import json

class CommandCategory(Enum):
    """Categories of commands."""
    COST_ANALYSIS = "cost_analysis"
    RESOURCE_MANAGEMENT = "resource_management"
    MONITORING = "monitoring"
    SECURITY = "security"
    OPTIMIZATION = "optimization"
    REPORTING = "reporting"

@dataclass
class CommandSuggestion:
    """Represents a command suggestion."""
    command: str
    description: str
    category: CommandCategory
    examples: List[str]
    parameters: List[str] = None

@dataclass
class InteractionHistory:
    """Represents user interaction history."""
    timestamp: datetime
    user_input: str
    assistant_response: str
    tools_used: List[str]
    success: bool
    session_id: str

class CommandSuggestionEngine:
    """Provides intelligent command suggestions based on context."""
    
    def __init__(self):
        self.commands = self._initialize_commands()
        self.interaction_patterns = {}
        
    def _initialize_commands(self) -> List[CommandSuggestion]:
        """Initialize the command database."""
        return [
            # Cost Analysis Commands
            CommandSuggestion(
                command="analyze costs",
                description="Get comprehensive cost analysis",
                category=CommandCategory.COST_ANALYSIS,
                examples=[
                    "analyze costs for last 30 days",
                    "analyze ec2 costs by region",
                    "analyze s3 storage costs"
                ],
                parameters=["service", "time_period", "region"]
            ),
            CommandSuggestion(
                command="cost optimization",
                description="Get cost optimization recommendations",
                category=CommandCategory.OPTIMIZATION,
                examples=[
                    "suggest cost optimizations",
                    "find unused resources",
                    "analyze reserved instance opportunities"
                ]
            ),
            CommandSuggestion(
                command="compare costs",
                description="Compare costs between services or regions",
                category=CommandCategory.COST_ANALYSIS,
                examples=[
                    "compare costs between us-east-1 and us-west-2",
                    "compare ec2 vs lambda costs",
                    "compare current vs last month costs"
                ]
            ),
            
            # Resource Management Commands
            CommandSuggestion(
                command="list resources",
                description="List AWS resources",
                category=CommandCategory.RESOURCE_MANAGEMENT,
                examples=[
                    "list all ec2 instances",
                    "show s3 buckets",
                    "list rds databases",
                    "show lambda functions"
                ],
                parameters=["service", "region", "status"]
            ),
            CommandSuggestion(
                command="resource details",
                description="Get detailed information about specific resources",
                category=CommandCategory.RESOURCE_MANAGEMENT,
                examples=[
                    "show details for instance i-1234567890abcdef0",
                    "describe bucket my-s3-bucket",
                    "get rds instance details"
                ]
            ),
            
            # Monitoring Commands
            CommandSuggestion(
                command="show dashboard",
                description="Display monitoring dashboards",
                category=CommandCategory.MONITORING,
                examples=[
                    "show infrastructure dashboard",
                    "display cost dashboard",
                    "show performance metrics"
                ]
            ),
            CommandSuggestion(
                command="health check",
                description="Check health status of resources",
                category=CommandCategory.MONITORING,
                examples=[
                    "check system health",
                    "show service status",
                    "display alerts"
                ]
            ),
            
            # Security Commands
            CommandSuggestion(
                command="security scan",
                description="Perform security analysis",
                category=CommandCategory.SECURITY,
                examples=[
                    "run security scan",
                    "check compliance status",
                    "find security vulnerabilities"
                ]
            ),
            CommandSuggestion(
                command="access review",
                description="Review access permissions and policies",
                category=CommandCategory.SECURITY,
                examples=[
                    "review iam policies",
                    "check public access",
                    "audit permissions"
                ]
            ),
            
            # Reporting Commands
            CommandSuggestion(
                command="generate report",
                description="Generate various types of reports",
                category=CommandCategory.REPORTING,
                examples=[
                    "generate cost report",
                    "create infrastructure report",
                    "export security findings"
                ]
            )
        ]
    
    def get_suggestions(self, user_input: str, context: Dict[str, Any] = None) -> List[CommandSuggestion]:
        """Get command suggestions based on user input and context."""
        suggestions = []
        
        # Normalize input
        input_lower = user_input.lower()
        
        # Keyword-based matching
        for command in self.commands:
            score = self._calculate_relevance_score(input_lower, command)
            if score > 0.3:  # Threshold for relevance
                suggestions.append((command, score))
        
        # Sort by relevance score
        suggestions.sort(key=lambda x: x[1], reverse=True)
        
        # Return top suggestions
        return [cmd for cmd, score in suggestions[:5]]
    
    def _calculate_relevance_score(self, user_input: str, command: CommandSuggestion) -> float:
        """Calculate relevance score for a command."""
        score = 0.0
        
        # Check command name
        if any(word in user_input for word in command.command.lower().split()):
            score += 0.5
        
        # Check description
        if any(word in user_input for word in command.description.lower().split()):
            score += 0.3
        
        # Check examples
        for example in command.examples:
            if any(word in user_input for word in example.lower().split()):
                score += 0.2
                break
        
        # Category-specific keywords
        category_keywords = {
            CommandCategory.COST_ANALYSIS: ["cost", "price", "billing", "spend", "budget"],
            CommandCategory.RESOURCE_MANAGEMENT: ["instance", "bucket", "database", "resource"],
            CommandCategory.MONITORING: ["dashboard", "metrics", "performance", "health"],
            CommandCategory.SECURITY: ["security", "compliance", "access", "permission"],
            CommandCategory.OPTIMIZATION: ["optimize", "improve", "efficiency", "recommendation"],
            CommandCategory.REPORTING: ["report", "export", "generate", "download"]
        }
        
        keywords = category_keywords.get(command.category, [])
        if any(keyword in user_input for keyword in keywords):
            score += 0.4
        
        return min(score, 1.0)  # Cap at 1.0

class NavigationHelper:
    """Provides navigation assistance and breadcrumbs."""
    
    def __init__(self):
        self.navigation_stack = []
        self.current_context = None
    
    def push_context(self, context: str, data: Dict[str, Any] = None):
        """Push a new context to the navigation stack."""
        self.navigation_stack.append({
            "context": context,
            "data": data or {},
            "timestamp": datetime.now()
        })
        self.current_context = context
    
    def pop_context(self) -> Optional[Dict[str, Any]]:
        """Pop the current context and return to previous."""
        if len(self.navigation_stack) > 1:
            self.navigation_stack.pop()
            previous = self.navigation_stack[-1]
            self.current_context = previous["context"]
            return previous
        return None
    
    def get_breadcrumbs(self) -> str:
        """Get navigation breadcrumbs."""
        if not self.navigation_stack:
            return "🏠 Home"
        
        breadcrumbs = ["🏠 Home"]
        for item in self.navigation_stack:
            breadcrumbs.append(item["context"])
        
        return " > ".join(breadcrumbs)
    
    def get_navigation_actions(self) -> List[Dict[str, str]]:
        """Get available navigation actions."""
        actions = [
            {"name": "home", "label": "🏠 Home", "value": "navigate_home"}
        ]
        
        if len(self.navigation_stack) > 1:
            actions.append({
                "name": "back", 
                "label": "⬅️ Back", 
                "value": "navigate_back"
            })
        
        # Context-specific actions
        if self.current_context:
            context_actions = self._get_context_actions(self.current_context)
            actions.extend(context_actions)
        
        return actions
    
    def _get_context_actions(self, context: str) -> List[Dict[str, str]]:
        """Get actions specific to current context."""
        context_actions = {
            "Dashboard": [
                {"name": "refresh", "label": "🔄 Refresh", "value": "refresh_dashboard"},
                {"name": "export", "label": "📥 Export", "value": "export_dashboard"}
            ],
            "Cost Analysis": [
                {"name": "optimize", "label": "🎯 Optimize", "value": "cost_optimize"},
                {"name": "forecast", "label": "📈 Forecast", "value": "cost_forecast"}
            ],
            "Security": [
                {"name": "scan", "label": "🔍 Scan", "value": "security_scan"},
                {"name": "remediate", "label": "🛠️ Remediate", "value": "security_remediate"}
            ]
        }
        
        return context_actions.get(context, [])

class SearchEngine:
    """Provides search functionality across AWS resources and documentation."""
    
    def __init__(self):
        self.search_index = {}
        self.search_history = []
    
    def search(self, query: str, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Search for resources, documentation, or commands."""
        results = []
        
        # Search in different categories
        results.extend(self._search_resources(query, filters))
        results.extend(self._search_documentation(query))
        results.extend(self._search_commands(query))
        
        # Sort by relevance
        results.sort(key=lambda x: x.get('relevance', 0), reverse=True)
        
        # Store search in history
        self.search_history.append({
            "query": query,
            "timestamp": datetime.now(),
            "results_count": len(results)
        })
        
        return results[:20]  # Return top 20 results
    
    def _search_resources(self, query: str, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Search AWS resources."""
        # This would integrate with the AWS agent to search actual resources
        # For now, return mock results
        mock_resources = [
            {
                "type": "resource",
                "category": "EC2",
                "title": "Production Web Server",
                "description": "t3.medium instance running web application",
                "id": "i-1234567890abcdef0",
                "relevance": 0.8
            },
            {
                "type": "resource",
                "category": "S3",
                "title": "Application Logs Bucket",
                "description": "S3 bucket storing application logs",
                "id": "my-app-logs-bucket",
                "relevance": 0.7
            }
        ]
        
        # Filter based on query
        query_lower = query.lower()
        filtered_resources = []
        
        for resource in mock_resources:
            if (query_lower in resource["title"].lower() or 
                query_lower in resource["description"].lower() or
                query_lower in resource["category"].lower()):
                filtered_resources.append(resource)
        
        return filtered_resources
    
    def _search_documentation(self, query: str) -> List[Dict[str, Any]]:
        """Search documentation and help content."""
        docs = [
            {
                "type": "documentation",
                "category": "Help",
                "title": "Cost Analysis Guide",
                "description": "Learn how to analyze and optimize AWS costs",
                "url": "/help/cost-analysis",
                "relevance": 0.9 if "cost" in query.lower() else 0.3
            },
            {
                "type": "documentation",
                "category": "Help",
                "title": "Security Best Practices",
                "description": "AWS security recommendations and best practices",
                "url": "/help/security",
                "relevance": 0.9 if "security" in query.lower() else 0.3
            }
        ]
        
        return [doc for doc in docs if doc["relevance"] > 0.5]
    
    def _search_commands(self, query: str) -> List[Dict[str, Any]]:
        """Search available commands."""
        command_engine = CommandSuggestionEngine()
        suggestions = command_engine.get_suggestions(query)
        
        results = []
        for suggestion in suggestions:
            results.append({
                "type": "command",
                "category": suggestion.category.value,
                "title": suggestion.command,
                "description": suggestion.description,
                "examples": suggestion.examples,
                "relevance": 0.8
            })
        
        return results
    
    def get_search_suggestions(self, partial_query: str) -> List[str]:
        """Get search suggestions based on partial query."""
        suggestions = []
        
        # Common search terms
        common_terms = [
            "ec2 instances", "s3 buckets", "rds databases", "lambda functions",
            "cost analysis", "security scan", "performance metrics",
            "optimization recommendations", "billing report"
        ]
        
        partial_lower = partial_query.lower()
        for term in common_terms:
            if term.startswith(partial_lower) or partial_lower in term:
                suggestions.append(term)
        
        return suggestions[:5]

class HelpSystem:
    """Provides contextual help and documentation."""
    
    def __init__(self):
        self.help_topics = self._initialize_help_topics()
    
    def _initialize_help_topics(self) -> Dict[str, Dict[str, Any]]:
        """Initialize help topics."""
        return {
            "getting_started": {
                "title": "Getting Started",
                "content": """
# Getting Started with AWS Management Assistant

## Quick Start
1. Ask natural language questions about your AWS resources
2. Use the dashboard buttons for quick access to common features
3. Upload configuration files for analysis
4. Export reports for offline analysis

## Common Commands
- "Show me my EC2 instances"
- "Analyze costs for last month"
- "Check security status"
- "Generate cost optimization report"
""",
                "related": ["commands", "dashboard", "cost_analysis"]
            },
            "commands": {
                "title": "Available Commands",
                "content": """
# Available Commands

## Cost Analysis
- `analyze costs [service] [time_period]`
- `cost optimization suggestions`
- `compare costs [region1] vs [region2]`

## Resource Management
- `list [service] resources`
- `show details for [resource_id]`
- `resource utilization metrics`

## Security
- `security scan`
- `compliance status`
- `access review`

## Monitoring
- `show dashboard`
- `performance metrics`
- `health check`
""",
                "related": ["getting_started", "examples"]
            },
            "cost_analysis": {
                "title": "Cost Analysis",
                "content": """
# Cost Analysis Features

## Available Analysis Types
- Service-specific cost breakdown
- Regional cost comparison
- Time-based cost trends
- Optimization recommendations

## Time Periods
- Last 7 days, 30 days, 90 days
- Custom date ranges
- Month-over-month comparison

## Export Options
- JSON, CSV, Excel formats
- Detailed reports with charts
- Summary dashboards
""",
                "related": ["optimization", "reporting"]
            }
        }
    
    def get_help(self, topic: str = None) -> str:
        """Get help content for a specific topic."""
        if topic and topic in self.help_topics:
            help_data = self.help_topics[topic]
            content = help_data["content"]
            
            # Add related topics
            if help_data.get("related"):
                content += "\n\n## Related Topics\n"
                for related in help_data["related"]:
                    if related in self.help_topics:
                        content += f"- {self.help_topics[related]['title']}\n"
            
            return content
        else:
            # Return general help
            return self._get_general_help()
    
    def _get_general_help(self) -> str:
        """Get general help overview."""
        return """
# AWS Management Assistant Help

## Available Help Topics
- **Getting Started**: Basic usage and quick start guide
- **Commands**: Complete list of available commands
- **Cost Analysis**: Cost analysis and optimization features
- **Security**: Security scanning and compliance features
- **Monitoring**: Dashboard and monitoring capabilities

## Quick Help
- Type your questions in natural language
- Use action buttons for quick access
- Upload files for analysis
- Export reports in various formats

## Need More Help?
Ask specific questions like:
- "How do I analyze costs?"
- "What security features are available?"
- "How do I export reports?"
"""
    
    def search_help(self, query: str) -> List[Dict[str, Any]]:
        """Search help content."""
        results = []
        query_lower = query.lower()
        
        for topic_id, topic_data in self.help_topics.items():
            title = topic_data["title"]
            content = topic_data["content"]
            
            # Calculate relevance
            relevance = 0
            if query_lower in title.lower():
                relevance += 0.5
            if query_lower in content.lower():
                relevance += 0.3
            
            if relevance > 0:
                results.append({
                    "topic_id": topic_id,
                    "title": title,
                    "relevance": relevance,
                    "snippet": self._extract_snippet(content, query_lower)
                })
        
        return sorted(results, key=lambda x: x["relevance"], reverse=True)
    
    def _extract_snippet(self, content: str, query: str) -> str:
        """Extract relevant snippet from content."""
        lines = content.split('\n')
        for line in lines:
            if query in line.lower():
                return line.strip()
        
        # Return first non-empty line if no match
        for line in lines:
            if line.strip() and not line.startswith('#'):
                return line.strip()
        
        return "No preview available"
