"""
AWS Agent Backend Integration Layer

This module provides comprehensive integration with the existing AWS agent backend,
including API client, message handling, streaming support, and error management.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, AsyncGenerator
from datetime import datetime, timedelta
import httpx
from dataclasses import dataclass
from enum import Enum
import uuid
import time

logger = logging.getLogger(__name__)

class MessageType(Enum):
    """Types of messages in the conversation."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    ERROR = "error"

class ToolExecutionStatus(Enum):
    """Status of tool execution."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class ToolExecution:
    """Represents a tool execution."""
    tool_name: str
    parameters: Dict[str, Any]
    status: ToolExecutionStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

@dataclass
class ConversationMessage:
    """Represents a message in the conversation."""
    id: str
    type: MessageType
    content: str
    timestamp: datetime
    session_id: str
    tool_executions: List[ToolExecution] = None
    metadata: Dict[str, Any] = None

class AWSAgentClient:
    """Enhanced client for communicating with the AWS Agent backend."""
    
    def __init__(self, base_url: str, timeout: int = 120):
        self.base_url = base_url.rstrip('/')
        self.timeout = httpx.Timeout(timeout)
        self.session_cache = {}
        self.health_status = None
        self.last_health_check = None
        
    async def health_check(self, force_refresh: bool = False) -> Dict[str, Any]:
        """Check the health of the AWS Agent backend with caching."""
        now = datetime.now()
        
        # Use cached result if recent and not forcing refresh
        if (not force_refresh and 
            self.last_health_check and 
            self.health_status and
            (now - self.last_health_check).seconds < 30):
            return self.health_status
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                response = await client.get(f"{self.base_url}/health")
                response.raise_for_status()
                
                self.health_status = response.json()
                self.last_health_check = now
                
                logger.info(f"Health check successful: {self.health_status}")
                return self.health_status
                
            except httpx.TimeoutException:
                error_status = {
                    "status": "timeout",
                    "error": "Backend service timeout",
                    "timestamp": now.isoformat()
                }
                logger.error("Health check timeout")
                return error_status
                
            except httpx.ConnectError:
                error_status = {
                    "status": "connection_error",
                    "error": "Cannot connect to backend service",
                    "timestamp": now.isoformat()
                }
                logger.error("Health check connection error")
                return error_status
                
            except Exception as e:
                error_status = {
                    "status": "error",
                    "error": str(e),
                    "timestamp": now.isoformat()
                }
                logger.error(f"Health check failed: {e}")
                return error_status
    
    async def send_message(self, message: str, session_id: str, 
                          stream: bool = False) -> Dict[str, Any]:
        """Send a message to the AWS Agent and get response."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                payload = {
                    "message": message,
                    "session_id": session_id,
                    "stream": stream
                }
                
                logger.info(f"Sending message to AWS Agent: {message[:100]}...")
                
                response = await client.post(f"{self.base_url}/chat", json=payload)
                response.raise_for_status()
                
                result = response.json()
                
                # Parse tool executions if present
                if "tools_used" in result:
                    result["parsed_tools"] = self._parse_tool_executions(result["tools_used"])
                
                logger.info(f"Received response from AWS Agent: {len(result.get('response', ''))} chars")
                return result
                
            except httpx.TimeoutException:
                logger.error("Message sending timeout")
                return {
                    "status": "timeout",
                    "error": "Request timeout - the AWS agent took too long to respond",
                    "session_id": session_id
                }
                
            except httpx.HTTPStatusError as e:
                logger.error(f"HTTP error: {e.response.status_code}")
                return {
                    "status": "http_error",
                    "error": f"HTTP {e.response.status_code}: {e.response.text}",
                    "session_id": session_id
                }
                
            except Exception as e:
                logger.error(f"Message sending failed: {e}")
                return {
                    "status": "error",
                    "error": str(e),
                    "session_id": session_id
                }
    
    async def stream_message(self, message: str, session_id: str) -> AsyncGenerator[str, None]:
        """Stream a message response from the AWS Agent."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                url = f"{self.base_url}/chat/{session_id}/stream"
                params = {"message": message}
                
                async with client.stream("GET", url, params=params) as response:
                    response.raise_for_status()
                    
                    async for chunk in response.aiter_text():
                        if chunk.strip():
                            # Parse SSE format
                            if chunk.startswith("data: "):
                                data_str = chunk[6:].strip()
                                try:
                                    data = json.loads(data_str)
                                    if data.get("chunk"):
                                        yield data["chunk"]
                                    if data.get("done"):
                                        break
                                except json.JSONDecodeError:
                                    continue
                                    
            except Exception as e:
                logger.error(f"Streaming failed: {e}")
                yield f"Error: {str(e)}"
    
    async def get_session_stats(self, session_id: str) -> Dict[str, Any]:
        """Get session statistics and metrics."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                response = await client.get(f"{self.base_url}/sessions/{session_id}/stats")
                if response.status_code == 200:
                    return response.json()
                else:
                    return {
                        "tools_used": 0,
                        "messages": 0,
                        "cost_estimate": 0.0,
                        "session_duration": 0
                    }
            except Exception as e:
                logger.error(f"Session stats failed: {e}")
                return {
                    "tools_used": 0,
                    "messages": 0,
                    "cost_estimate": 0.0,
                    "session_duration": 0,
                    "error": str(e)
                }
    
    async def get_available_tools(self) -> List[Dict[str, Any]]:
        """Get list of available tools from the backend."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                response = await client.get(f"{self.base_url}/tools")
                if response.status_code == 200:
                    return response.json().get("tools", [])
                else:
                    return []
            except Exception as e:
                logger.error(f"Failed to get available tools: {e}")
                return []
    
    def _parse_tool_executions(self, tools_used: List[str]) -> List[ToolExecution]:
        """Parse tool execution information."""
        executions = []
        for tool_name in tools_used:
            execution = ToolExecution(
                tool_name=tool_name,
                parameters={},
                status=ToolExecutionStatus.COMPLETED,
                start_time=datetime.now(),
                end_time=datetime.now()
            )
            executions.append(execution)
        return executions

class ConversationManager:
    """Manages conversation state and history."""
    
    def __init__(self, max_history: int = 50):
        self.max_history = max_history
        self.messages: List[ConversationMessage] = []
        self.session_metadata = {}
    
    def add_message(self, message_type: MessageType, content: str, 
                   session_id: str, tool_executions: List[ToolExecution] = None,
                   metadata: Dict[str, Any] = None) -> ConversationMessage:
        """Add a message to the conversation."""
        message = ConversationMessage(
            id=str(uuid.uuid4()),
            type=message_type,
            content=content,
            timestamp=datetime.now(),
            session_id=session_id,
            tool_executions=tool_executions or [],
            metadata=metadata or {}
        )
        
        self.messages.append(message)
        
        # Trim history if needed
        if len(self.messages) > self.max_history:
            self.messages = self.messages[-self.max_history:]
        
        return message
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """Get a summary of the conversation."""
        if not self.messages:
            return {
                "total_messages": 0,
                "user_messages": 0,
                "assistant_messages": 0,
                "tools_used": 0,
                "conversation_duration": 0
            }
        
        user_messages = len([m for m in self.messages if m.type == MessageType.USER])
        assistant_messages = len([m for m in self.messages if m.type == MessageType.ASSISTANT])
        
        total_tools = sum(len(m.tool_executions) for m in self.messages)
        
        duration = (self.messages[-1].timestamp - self.messages[0].timestamp).total_seconds()
        
        return {
            "total_messages": len(self.messages),
            "user_messages": user_messages,
            "assistant_messages": assistant_messages,
            "tools_used": total_tools,
            "conversation_duration": duration,
            "start_time": self.messages[0].timestamp.isoformat(),
            "last_activity": self.messages[-1].timestamp.isoformat()
        }
    
    def get_recent_context(self, max_messages: int = 10) -> List[Dict[str, Any]]:
        """Get recent conversation context for the backend."""
        recent_messages = self.messages[-max_messages:]
        
        context = []
        for msg in recent_messages:
            context.append({
                "type": msg.type.value,
                "content": msg.content,
                "timestamp": msg.timestamp.isoformat(),
                "tools_used": [t.tool_name for t in msg.tool_executions]
            })
        
        return context

class ResponseFormatter:
    """Formats responses from the AWS Agent for display."""
    
    @staticmethod
    def format_response(response: Dict[str, Any]) -> str:
        """Format AWS Agent response for better display."""
        if response.get("status") == "error":
            return f"❌ **Error**: {response.get('error', 'Unknown error occurred')}"
        
        content = response.get("response", "")
        tools_used = response.get("tools_used", [])
        parsed_tools = response.get("parsed_tools", [])
        
        # Add tool usage information
        if tools_used:
            tool_info = "\n\n---\n**🔧 Tools Used**: " + ", ".join(tools_used)
            content += tool_info
        
        # Add detailed tool execution info if available
        if parsed_tools:
            content += "\n\n**📊 Tool Execution Details**:"
            for tool in parsed_tools:
                status_emoji = "✅" if tool.status == ToolExecutionStatus.COMPLETED else "❌"
                content += f"\n- {status_emoji} {tool.tool_name}"
        
        # Add session information
        session_id = response.get("session_id", "")
        if session_id:
            content += f"\n\n*Session: {session_id[:8]}...*"
        
        return content
    
    @staticmethod
    def format_error(error: str, context: str = "") -> str:
        """Format error messages consistently."""
        error_msg = f"❌ **Error**: {error}"
        if context:
            error_msg += f"\n\n**Context**: {context}"
        return error_msg
    
    @staticmethod
    def format_tool_execution(tool: ToolExecution) -> str:
        """Format individual tool execution for display."""
        status_emoji = {
            ToolExecutionStatus.PENDING: "⏳",
            ToolExecutionStatus.RUNNING: "🔄",
            ToolExecutionStatus.COMPLETED: "✅",
            ToolExecutionStatus.FAILED: "❌"
        }.get(tool.status, "❓")
        
        duration = ""
        if tool.end_time and tool.start_time:
            duration_seconds = (tool.end_time - tool.start_time).total_seconds()
            duration = f" ({duration_seconds:.2f}s)"
        
        return f"{status_emoji} **{tool.tool_name}**{duration}"

# Utility functions
async def test_backend_connection(base_url: str) -> Dict[str, Any]:
    """Test connection to the AWS Agent backend."""
    client = AWSAgentClient(base_url)
    return await client.health_check(force_refresh=True)

def create_session_id() -> str:
    """Create a new session ID."""
    return str(uuid.uuid4())

def format_timestamp(timestamp: datetime) -> str:
    """Format timestamp for display."""
    return timestamp.strftime("%Y-%m-%d %H:%M:%S")
