# Chainlit AWS Management Frontend Requirements

# Core Chainlit Framework
chainlit>=1.0.0

# Existing backend dependencies
fastapi>=0.110.0
uvicorn[standard]>=0.27.0
pydantic>=2.6.0
python-dotenv>=1.0.0
boto3>=1.34.0
mcp>=1.0.0
aiofiles>=23.2.1
python-multipart>=0.0.6
anyio>=4.6

# Additional frontend dependencies
streamlit>=1.28.0
plotly>=5.17.0
pandas>=2.1.0
numpy>=1.24.0
requests>=2.31.0

# Data visualization and charts
matplotlib>=3.7.0
seaborn>=0.12.0
altair>=5.1.0

# Authentication and security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# File handling and utilities
openpyxl>=3.1.0
xlsxwriter>=3.1.0
PyPDF2>=3.0.0
python-magic>=0.4.27

# HTTP client and async support
httpx>=0.25.0
websockets>=11.0.0

# Logging and monitoring
structlog>=23.1.0
prometheus-client>=0.17.0

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0
