# Chainlit AWS Management Frontend Requirements
# Constrained versions to avoid dependency resolution issues

# Core Chainlit Framework
chainlit>=1.0.0,<2.0.0

# Backend dependencies (matching existing requirements)
fastapi>=0.110.0,<0.115.0
uvicorn[standard]>=0.27.0,<0.32.0
pydantic>=2.6.0,<3.0.0
python-dotenv>=1.0.0,<2.0.0
boto3>=1.34.0,<2.0.0
aiofiles>=23.2.1,<24.0.0
anyio>=4.6.0,<5.0.0

# HTTP client and async support
httpx>=0.25.0,<0.28.0
websockets>=11.0.0,<13.0.0

# Data processing and visualization
pandas>=2.1.0,<3.0.0
numpy>=1.24.0,<2.0.0
plotly>=5.17.0,<6.0.0

# File handling utilities
openpyxl>=3.1.0,<4.0.0
xlsxwriter>=3.1.0,<4.0.0
PyPDF2>=3.0.0,<4.0.0

# Authentication and security
python-jose[cryptography]>=3.3.0,<4.0.0
passlib[bcrypt]>=1.7.4,<2.0.0

# Logging and monitoring
structlog>=23.1.0,<25.0.0
prometheus-client>=0.17.0,<1.0.0

# Development tools (optional)
pytest>=7.4.0,<9.0.0
pytest-asyncio>=0.21.0,<1.0.0
