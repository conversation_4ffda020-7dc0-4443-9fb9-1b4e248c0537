"""
AWS Resource Display Components

This module provides structured display components for AWS resources
including tables, cards, and formatted responses for various AWS services.
"""

import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime
import json
from dataclasses import dataclass
from enum import Enum

class ResourceStatus(Enum):
    """Status of AWS resources."""
    RUNNING = "running"
    STOPPED = "stopped"
    PENDING = "pending"
    TERMINATED = "terminated"
    AVAILABLE = "available"
    UNAVAILABLE = "unavailable"
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"

@dataclass
class AWSResource:
    """Base class for AWS resources."""
    id: str
    name: str
    type: str
    status: ResourceStatus
    region: str
    tags: Dict[str, str] = None
    created_at: Optional[datetime] = None
    cost_estimate: Optional[float] = None

class ResourceDisplayFormatter:
    """Formats AWS resources for display in various formats."""
    
    def __init__(self):
        self.status_emojis = {
            ResourceStatus.RUNNING: "🟢",
            ResourceStatus.STOPPED: "🔴",
            ResourceStatus.PENDING: "🟡",
            ResourceStatus.TERMINATED: "⚫",
            ResourceStatus.AVAILABLE: "🟢",
            ResourceStatus.UNAVAILABLE: "🔴",
            ResourceStatus.HEALTHY: "💚",
            ResourceStatus.UNHEALTHY: "❤️"
        }
    
    def format_ec2_instances(self, instances: List[Dict[str, Any]]) -> str:
        """Format EC2 instances for display."""
        if not instances:
            return "No EC2 instances found."
        
        output = "# 🖥️ EC2 Instances\n\n"
        
        for instance in instances:
            status = instance.get('state', 'unknown')
            status_emoji = self._get_status_emoji(status)
            
            output += f"## {status_emoji} {instance.get('name', instance.get('id', 'Unknown'))}\n"
            output += f"- **Instance ID**: `{instance.get('id', 'N/A')}`\n"
            output += f"- **Type**: {instance.get('instance_type', 'N/A')}\n"
            output += f"- **Status**: {status.title()}\n"
            output += f"- **Region**: {instance.get('region', 'N/A')}\n"
            output += f"- **Launch Time**: {instance.get('launch_time', 'N/A')}\n"
            
            if 'public_ip' in instance:
                output += f"- **Public IP**: {instance['public_ip']}\n"
            if 'private_ip' in instance:
                output += f"- **Private IP**: {instance['private_ip']}\n"
            
            # Cost information
            if 'cost_estimate' in instance:
                output += f"- **Monthly Cost**: ${instance['cost_estimate']:.2f}\n"
            
            # Tags
            if instance.get('tags'):
                output += "- **Tags**: "
                tags = [f"`{k}:{v}`" for k, v in instance['tags'].items()]
                output += ", ".join(tags) + "\n"
            
            output += "\n"
        
        return output
    
    def format_s3_buckets(self, buckets: List[Dict[str, Any]]) -> str:
        """Format S3 buckets for display."""
        if not buckets:
            return "No S3 buckets found."
        
        output = "# 🪣 S3 Buckets\n\n"
        
        for bucket in buckets:
            output += f"## 📦 {bucket.get('name', 'Unknown Bucket')}\n"
            output += f"- **Bucket Name**: `{bucket.get('name', 'N/A')}`\n"
            output += f"- **Region**: {bucket.get('region', 'N/A')}\n"
            output += f"- **Created**: {bucket.get('creation_date', 'N/A')}\n"
            
            # Storage information
            if 'size_bytes' in bucket:
                size_gb = bucket['size_bytes'] / (1024**3)
                output += f"- **Size**: {size_gb:.2f} GB\n"
            
            if 'object_count' in bucket:
                output += f"- **Objects**: {bucket['object_count']:,}\n"
            
            # Cost information
            if 'storage_cost' in bucket:
                output += f"- **Storage Cost**: ${bucket['storage_cost']:.2f}/month\n"
            if 'transfer_cost' in bucket:
                output += f"- **Transfer Cost**: ${bucket['transfer_cost']:.2f}/month\n"
            
            # Security and configuration
            if 'public_access' in bucket:
                access_emoji = "🔓" if bucket['public_access'] else "🔒"
                output += f"- **Public Access**: {access_emoji} {'Enabled' if bucket['public_access'] else 'Disabled'}\n"
            
            if 'versioning' in bucket:
                output += f"- **Versioning**: {'✅' if bucket['versioning'] else '❌'}\n"
            
            output += "\n"
        
        return output
    
    def format_rds_instances(self, instances: List[Dict[str, Any]]) -> str:
        """Format RDS instances for display."""
        if not instances:
            return "No RDS instances found."
        
        output = "# 🗄️ RDS Database Instances\n\n"
        
        for instance in instances:
            status = instance.get('status', 'unknown')
            status_emoji = self._get_status_emoji(status)
            
            output += f"## {status_emoji} {instance.get('identifier', 'Unknown DB')}\n"
            output += f"- **DB Identifier**: `{instance.get('identifier', 'N/A')}`\n"
            output += f"- **Engine**: {instance.get('engine', 'N/A')} {instance.get('engine_version', '')}\n"
            output += f"- **Instance Class**: {instance.get('instance_class', 'N/A')}\n"
            output += f"- **Status**: {status.title()}\n"
            output += f"- **Region**: {instance.get('region', 'N/A')}\n"
            
            # Storage information
            if 'allocated_storage' in instance:
                output += f"- **Storage**: {instance['allocated_storage']} GB\n"
            if 'storage_type' in instance:
                output += f"- **Storage Type**: {instance['storage_type']}\n"
            
            # Connection information
            if 'endpoint' in instance:
                output += f"- **Endpoint**: `{instance['endpoint']}`\n"
            if 'port' in instance:
                output += f"- **Port**: {instance['port']}\n"
            
            # Performance and configuration
            if 'multi_az' in instance:
                output += f"- **Multi-AZ**: {'✅' if instance['multi_az'] else '❌'}\n"
            if 'backup_retention' in instance:
                output += f"- **Backup Retention**: {instance['backup_retention']} days\n"
            
            # Cost information
            if 'monthly_cost' in instance:
                output += f"- **Monthly Cost**: ${instance['monthly_cost']:.2f}\n"
            
            output += "\n"
        
        return output
    
    def format_lambda_functions(self, functions: List[Dict[str, Any]]) -> str:
        """Format Lambda functions for display."""
        if not functions:
            return "No Lambda functions found."
        
        output = "# ⚡ Lambda Functions\n\n"
        
        for function in functions:
            output += f"## 🔧 {function.get('name', 'Unknown Function')}\n"
            output += f"- **Function Name**: `{function.get('name', 'N/A')}`\n"
            output += f"- **Runtime**: {function.get('runtime', 'N/A')}\n"
            output += f"- **Memory**: {function.get('memory_size', 'N/A')} MB\n"
            output += f"- **Timeout**: {function.get('timeout', 'N/A')} seconds\n"
            
            # Performance metrics
            if 'invocations' in function:
                output += f"- **Invocations (30d)**: {function['invocations']:,}\n"
            if 'duration_avg' in function:
                output += f"- **Avg Duration**: {function['duration_avg']:.2f} ms\n"
            if 'error_rate' in function:
                output += f"- **Error Rate**: {function['error_rate']:.2f}%\n"
            
            # Cost information
            if 'monthly_cost' in function:
                output += f"- **Monthly Cost**: ${function['monthly_cost']:.2f}\n"
            
            # Configuration
            if 'last_modified' in function:
                output += f"- **Last Modified**: {function['last_modified']}\n"
            if 'code_size' in function:
                output += f"- **Code Size**: {function['code_size'] / 1024:.1f} KB\n"
            
            output += "\n"
        
        return output
    
    def format_cost_summary(self, cost_data: Dict[str, Any]) -> str:
        """Format cost summary information."""
        output = "# 💰 AWS Cost Summary\n\n"
        
        if 'total_cost' in cost_data:
            output += f"## 📊 **Total Monthly Cost**: ${cost_data['total_cost']:.2f}\n\n"
        
        if 'service_breakdown' in cost_data:
            output += "## 🔧 Cost by Service\n\n"
            for service, cost in cost_data['service_breakdown'].items():
                percentage = (cost / cost_data.get('total_cost', 1)) * 100
                output += f"- **{service}**: ${cost:.2f} ({percentage:.1f}%)\n"
            output += "\n"
        
        if 'region_breakdown' in cost_data:
            output += "## 🌍 Cost by Region\n\n"
            for region, cost in cost_data['region_breakdown'].items():
                percentage = (cost / cost_data.get('total_cost', 1)) * 100
                output += f"- **{region}**: ${cost:.2f} ({percentage:.1f}%)\n"
            output += "\n"
        
        if 'trends' in cost_data:
            output += "## 📈 Cost Trends\n\n"
            trends = cost_data['trends']
            if 'monthly_change' in trends:
                change = trends['monthly_change']
                trend_emoji = "📈" if change > 0 else "📉" if change < 0 else "➡️"
                output += f"- **Monthly Change**: {trend_emoji} {change:+.1f}%\n"
            
            if 'projected_monthly' in trends:
                output += f"- **Projected Monthly**: ${trends['projected_monthly']:.2f}\n"
        
        return output
    
    def format_optimization_recommendations(self, recommendations: List[Dict[str, Any]]) -> str:
        """Format optimization recommendations."""
        if not recommendations:
            return "No optimization recommendations available."
        
        output = "# 🎯 Cost Optimization Recommendations\n\n"
        
        for i, rec in enumerate(recommendations, 1):
            priority_emoji = {
                'high': '🔴',
                'medium': '🟡',
                'low': '🟢'
            }.get(rec.get('priority', 'medium'), '🟡')
            
            output += f"## {priority_emoji} Recommendation {i}: {rec.get('title', 'Optimization Opportunity')}\n\n"
            output += f"**Priority**: {rec.get('priority', 'Medium').title()}\n\n"
            
            if 'description' in rec:
                output += f"**Description**: {rec['description']}\n\n"
            
            if 'potential_savings' in rec:
                output += f"**💰 Potential Savings**: ${rec['potential_savings']:.2f}/month\n\n"
            
            if 'effort' in rec:
                output += f"**⏱️ Implementation Effort**: {rec['effort']}\n\n"
            
            if 'steps' in rec:
                output += "**📋 Implementation Steps**:\n"
                for step in rec['steps']:
                    output += f"1. {step}\n"
                output += "\n"
            
            if 'resources_affected' in rec:
                output += f"**🎯 Resources Affected**: {', '.join(rec['resources_affected'])}\n\n"
            
            output += "---\n\n"
        
        return output
    
    def _get_status_emoji(self, status: str) -> str:
        """Get emoji for resource status."""
        status_lower = status.lower()
        
        if status_lower in ['running', 'available', 'healthy']:
            return "🟢"
        elif status_lower in ['stopped', 'unavailable', 'unhealthy']:
            return "🔴"
        elif status_lower in ['pending', 'starting', 'stopping']:
            return "🟡"
        elif status_lower in ['terminated', 'deleted']:
            return "⚫"
        else:
            return "❓"
    
    def create_resource_table(self, resources: List[Dict[str, Any]], 
                            columns: List[str]) -> str:
        """Create a markdown table for resources."""
        if not resources:
            return "No resources to display."
        
        # Create DataFrame
        df = pd.DataFrame(resources)
        
        # Select only specified columns that exist
        available_columns = [col for col in columns if col in df.columns]
        if not available_columns:
            return "No matching columns found in resource data."
        
        df_display = df[available_columns]
        
        # Convert to markdown table
        return df_display.to_markdown(index=False)

class ResourceCardGenerator:
    """Generates card-style displays for AWS resources."""
    
    @staticmethod
    def create_instance_card(instance: Dict[str, Any]) -> str:
        """Create a card display for an EC2 instance."""
        status_emoji = "🟢" if instance.get('state') == 'running' else "🔴"
        
        card = f"""
┌─────────────────────────────────────────┐
│ {status_emoji} **{instance.get('name', instance.get('id', 'Unknown'))}**
├─────────────────────────────────────────┤
│ **Type**: {instance.get('instance_type', 'N/A')}
│ **Status**: {instance.get('state', 'Unknown').title()}
│ **Region**: {instance.get('region', 'N/A')}
│ **Cost**: ${instance.get('cost_estimate', 0):.2f}/month
└─────────────────────────────────────────┘
"""
        return card
    
    @staticmethod
    def create_service_summary_card(service_name: str, metrics: Dict[str, Any]) -> str:
        """Create a summary card for an AWS service."""
        card = f"""
┌─────────────────────────────────────────┐
│ 🔧 **{service_name}**
├─────────────────────────────────────────┤
│ **Resources**: {metrics.get('count', 0)}
│ **Monthly Cost**: ${metrics.get('cost', 0):.2f}
│ **Status**: {metrics.get('status', 'Unknown')}
│ **Health**: {metrics.get('health', 'Unknown')}
└─────────────────────────────────────────┘
"""
        return card

# Utility functions for data processing
def parse_aws_response(response_text: str) -> Dict[str, Any]:
    """Parse AWS agent response to extract structured data."""
    try:
        # Try to find JSON data in the response
        import re
        json_pattern = r'\{.*\}'
        matches = re.findall(json_pattern, response_text, re.DOTALL)
        
        if matches:
            return json.loads(matches[0])
        else:
            return {"raw_text": response_text}
    except:
        return {"raw_text": response_text}

def extract_resource_data(response: Dict[str, Any], resource_type: str) -> List[Dict[str, Any]]:
    """Extract specific resource data from AWS agent response."""
    # This would be customized based on the actual response format
    # from your AWS agent
    return response.get(resource_type, [])
