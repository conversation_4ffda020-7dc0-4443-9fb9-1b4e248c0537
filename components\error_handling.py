"""
Comprehensive Error Handling and Loading States

This module provides robust error handling, loading state management,
and responsive design utilities for the Chainlit AWS management application.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import traceback
import chainlit as cl

logger = logging.getLogger(__name__)

class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """Categories of errors."""
    NETWORK = "network"
    AUTHENTICATION = "authentication"
    PERMISSION = "permission"
    VALIDATION = "validation"
    SYSTEM = "system"
    AWS_SERVICE = "aws_service"
    USER_INPUT = "user_input"

@dataclass
class ErrorInfo:
    """Comprehensive error information."""
    error_id: str
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    technical_details: str
    user_message: str
    suggestions: List[str]
    timestamp: datetime
    context: Dict[str, Any] = None

class ErrorHandler:
    """Centralized error handling system."""
    
    def __init__(self):
        self.error_history = []
        self.error_patterns = self._initialize_error_patterns()
        self.recovery_strategies = self._initialize_recovery_strategies()
    
    def _initialize_error_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Initialize common error patterns and their handling."""
        return {
            "connection_timeout": {
                "category": ErrorCategory.NETWORK,
                "severity": ErrorSeverity.MEDIUM,
                "user_message": "Connection timeout - the service is taking longer than expected to respond",
                "suggestions": [
                    "Check your internet connection",
                    "Try again in a few moments",
                    "Use the health check to verify service status"
                ]
            },
            "connection_refused": {
                "category": ErrorCategory.NETWORK,
                "severity": ErrorSeverity.HIGH,
                "user_message": "Cannot connect to AWS Agent service",
                "suggestions": [
                    "Verify the AWS Agent service is running",
                    "Check the service URL configuration",
                    "Contact your system administrator"
                ]
            },
            "authentication_failed": {
                "category": ErrorCategory.AUTHENTICATION,
                "severity": ErrorSeverity.HIGH,
                "user_message": "Authentication failed - invalid credentials",
                "suggestions": [
                    "Verify your AWS credentials",
                    "Check if your session has expired",
                    "Contact your administrator for access"
                ]
            },
            "permission_denied": {
                "category": ErrorCategory.PERMISSION,
                "severity": ErrorSeverity.MEDIUM,
                "user_message": "Permission denied - insufficient privileges",
                "suggestions": [
                    "Check your AWS IAM permissions",
                    "Request additional access from your administrator",
                    "Try a different operation you have permissions for"
                ]
            },
            "invalid_input": {
                "category": ErrorCategory.USER_INPUT,
                "severity": ErrorSeverity.LOW,
                "user_message": "Invalid input provided",
                "suggestions": [
                    "Check the format of your input",
                    "Use the help command for examples",
                    "Try rephrasing your request"
                ]
            },
            "service_unavailable": {
                "category": ErrorCategory.AWS_SERVICE,
                "severity": ErrorSeverity.HIGH,
                "user_message": "AWS service is temporarily unavailable",
                "suggestions": [
                    "Check AWS service health status",
                    "Try again in a few minutes",
                    "Use alternative AWS regions if available"
                ]
            },
            "rate_limit_exceeded": {
                "category": ErrorCategory.AWS_SERVICE,
                "severity": ErrorSeverity.MEDIUM,
                "user_message": "Rate limit exceeded - too many requests",
                "suggestions": [
                    "Wait a moment before trying again",
                    "Reduce the frequency of your requests",
                    "Consider using batch operations"
                ]
            }
        }
    
    def _initialize_recovery_strategies(self) -> Dict[ErrorCategory, Callable]:
        """Initialize recovery strategies for different error categories."""
        return {
            ErrorCategory.NETWORK: self._handle_network_error,
            ErrorCategory.AUTHENTICATION: self._handle_auth_error,
            ErrorCategory.PERMISSION: self._handle_permission_error,
            ErrorCategory.AWS_SERVICE: self._handle_aws_service_error,
            ErrorCategory.USER_INPUT: self._handle_user_input_error
        }
    
    async def handle_error(self, error: Exception, context: Dict[str, Any] = None) -> ErrorInfo:
        """Handle an error and return structured error information."""
        try:
            # Generate error ID
            error_id = f"err_{int(datetime.now().timestamp())}"
            
            # Classify the error
            error_pattern = self._classify_error(error)
            
            # Create error info
            error_info = ErrorInfo(
                error_id=error_id,
                category=error_pattern["category"],
                severity=error_pattern["severity"],
                message=str(error),
                technical_details=traceback.format_exc(),
                user_message=error_pattern["user_message"],
                suggestions=error_pattern["suggestions"],
                timestamp=datetime.now(),
                context=context or {}
            )
            
            # Log the error
            self._log_error(error_info)
            
            # Store in history
            self.error_history.append(error_info)
            
            # Attempt recovery if possible
            await self._attempt_recovery(error_info)
            
            return error_info
            
        except Exception as e:
            # Fallback error handling
            logger.error(f"Error in error handler: {e}")
            return self._create_fallback_error_info(error)
    
    def _classify_error(self, error: Exception) -> Dict[str, Any]:
        """Classify an error based on its type and message."""
        error_str = str(error).lower()
        error_type = type(error).__name__.lower()
        
        # Check for specific error patterns
        if "timeout" in error_str or "timeout" in error_type:
            return self.error_patterns["connection_timeout"]
        elif "connection" in error_str and ("refused" in error_str or "failed" in error_str):
            return self.error_patterns["connection_refused"]
        elif "auth" in error_str or "credential" in error_str:
            return self.error_patterns["authentication_failed"]
        elif "permission" in error_str or "access denied" in error_str:
            return self.error_patterns["permission_denied"]
        elif "invalid" in error_str or "validation" in error_str:
            return self.error_patterns["invalid_input"]
        elif "service unavailable" in error_str or "503" in error_str:
            return self.error_patterns["service_unavailable"]
        elif "rate limit" in error_str or "throttl" in error_str:
            return self.error_patterns["rate_limit_exceeded"]
        else:
            # Default system error
            return {
                "category": ErrorCategory.SYSTEM,
                "severity": ErrorSeverity.MEDIUM,
                "user_message": "An unexpected error occurred",
                "suggestions": [
                    "Try your request again",
                    "Check the system status",
                    "Contact support if the problem persists"
                ]
            }
    
    def _log_error(self, error_info: ErrorInfo):
        """Log error information."""
        log_level = {
            ErrorSeverity.LOW: logging.INFO,
            ErrorSeverity.MEDIUM: logging.WARNING,
            ErrorSeverity.HIGH: logging.ERROR,
            ErrorSeverity.CRITICAL: logging.CRITICAL
        }.get(error_info.severity, logging.ERROR)
        
        logger.log(
            log_level,
            f"Error {error_info.error_id}: {error_info.category.value} - {error_info.message}"
        )
    
    async def _attempt_recovery(self, error_info: ErrorInfo):
        """Attempt to recover from the error."""
        recovery_strategy = self.recovery_strategies.get(error_info.category)
        if recovery_strategy:
            try:
                await recovery_strategy(error_info)
            except Exception as e:
                logger.error(f"Recovery strategy failed: {e}")
    
    async def _handle_network_error(self, error_info: ErrorInfo):
        """Handle network-related errors."""
        # Could implement retry logic, connection pooling, etc.
        pass
    
    async def _handle_auth_error(self, error_info: ErrorInfo):
        """Handle authentication errors."""
        # Could implement token refresh, re-authentication, etc.
        pass
    
    async def _handle_permission_error(self, error_info: ErrorInfo):
        """Handle permission errors."""
        # Could implement permission checking, alternative operations, etc.
        pass
    
    async def _handle_aws_service_error(self, error_info: ErrorInfo):
        """Handle AWS service errors."""
        # Could implement service health checking, region switching, etc.
        pass
    
    async def _handle_user_input_error(self, error_info: ErrorInfo):
        """Handle user input errors."""
        # Could implement input validation, suggestion generation, etc.
        pass
    
    def _create_fallback_error_info(self, error: Exception) -> ErrorInfo:
        """Create fallback error info when error handling fails."""
        return ErrorInfo(
            error_id=f"fallback_{int(datetime.now().timestamp())}",
            category=ErrorCategory.SYSTEM,
            severity=ErrorSeverity.HIGH,
            message=str(error),
            technical_details="Error handling failed",
            user_message="A system error occurred. Please try again.",
            suggestions=["Try your request again", "Contact support if the problem persists"],
            timestamp=datetime.now()
        )
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics for monitoring."""
        if not self.error_history:
            return {"total_errors": 0}
        
        # Calculate statistics
        total_errors = len(self.error_history)
        recent_errors = [e for e in self.error_history 
                        if e.timestamp > datetime.now() - timedelta(hours=24)]
        
        # Group by category
        category_counts = {}
        for error in self.error_history:
            category = error.category.value
            category_counts[category] = category_counts.get(category, 0) + 1
        
        # Group by severity
        severity_counts = {}
        for error in self.error_history:
            severity = error.severity.value
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        return {
            "total_errors": total_errors,
            "recent_errors_24h": len(recent_errors),
            "category_breakdown": category_counts,
            "severity_breakdown": severity_counts,
            "last_error": self.error_history[-1].timestamp.isoformat() if self.error_history else None
        }

class LoadingStateManager:
    """Manages loading states and progress indicators."""
    
    def __init__(self):
        self.active_operations = {}
    
    async def show_loading(self, operation_id: str, message: str, 
                          estimated_duration: Optional[int] = None) -> cl.Message:
        """Show a loading indicator for an operation."""
        loading_message = f"⏳ {message}"
        
        if estimated_duration:
            loading_message += f" (estimated: {estimated_duration}s)"
        
        msg = await cl.Message(content=loading_message).send()
        
        self.active_operations[operation_id] = {
            "message": msg,
            "start_time": datetime.now(),
            "estimated_duration": estimated_duration
        }
        
        return msg
    
    async def update_loading(self, operation_id: str, message: str, progress: Optional[int] = None):
        """Update a loading message with progress."""
        if operation_id not in self.active_operations:
            return
        
        operation = self.active_operations[operation_id]
        elapsed = (datetime.now() - operation["start_time"]).seconds
        
        update_message = f"⏳ {message}"
        
        if progress is not None:
            progress_bar = "█" * (progress // 10) + "░" * (10 - progress // 10)
            update_message += f"\n\n[{progress_bar}] {progress}%"
        
        update_message += f"\n\n*Elapsed: {elapsed}s*"
        
        await operation["message"].update(content=update_message)
    
    async def complete_loading(self, operation_id: str, success_message: str):
        """Complete a loading operation with success message."""
        if operation_id not in self.active_operations:
            return
        
        operation = self.active_operations[operation_id]
        elapsed = (datetime.now() - operation["start_time"]).seconds
        
        final_message = f"✅ {success_message}\n\n*Completed in {elapsed}s*"
        
        await operation["message"].update(content=final_message)
        del self.active_operations[operation_id]
    
    async def fail_loading(self, operation_id: str, error_message: str):
        """Complete a loading operation with error message."""
        if operation_id not in self.active_operations:
            return
        
        operation = self.active_operations[operation_id]
        elapsed = (datetime.now() - operation["start_time"]).seconds
        
        final_message = f"❌ {error_message}\n\n*Failed after {elapsed}s*"
        
        await operation["message"].update(content=final_message)
        del self.active_operations[operation_id]

class ResponsiveDesignHelper:
    """Helps with responsive design considerations."""
    
    @staticmethod
    def format_for_mobile(content: str, max_width: int = 50) -> str:
        """Format content for mobile display."""
        lines = content.split('\n')
        formatted_lines = []
        
        for line in lines:
            if len(line) <= max_width:
                formatted_lines.append(line)
            else:
                # Break long lines
                words = line.split(' ')
                current_line = ""
                
                for word in words:
                    if len(current_line + " " + word) <= max_width:
                        current_line += " " + word if current_line else word
                    else:
                        if current_line:
                            formatted_lines.append(current_line)
                        current_line = word
                
                if current_line:
                    formatted_lines.append(current_line)
        
        return '\n'.join(formatted_lines)
    
    @staticmethod
    def create_mobile_friendly_table(data: List[Dict[str, Any]], 
                                   max_columns: int = 3) -> str:
        """Create a mobile-friendly table representation."""
        if not data:
            return "No data available"
        
        # Limit columns for mobile
        headers = list(data[0].keys())[:max_columns]
        
        table_content = ""
        for item in data:
            table_content += "---\n"
            for header in headers:
                value = item.get(header, "N/A")
                table_content += f"**{header}**: {value}\n"
        
        return table_content
    
    @staticmethod
    def optimize_action_buttons(actions: List[cl.Action], 
                              max_buttons: int = 4) -> List[cl.Action]:
        """Optimize action buttons for mobile display."""
        if len(actions) <= max_buttons:
            return actions
        
        # Keep most important actions and add "More" button
        important_actions = actions[:max_buttons-1]
        important_actions.append(
            cl.Action(name="more_actions", value="show_more", label="⋯ More")
        )
        
        return important_actions

# Utility functions
async def safe_execute(func: Callable, *args, error_handler: ErrorHandler = None, **kwargs) -> Any:
    """Safely execute a function with error handling."""
    try:
        if asyncio.iscoroutinefunction(func):
            return await func(*args, **kwargs)
        else:
            return func(*args, **kwargs)
    except Exception as e:
        if error_handler:
            error_info = await error_handler.handle_error(e)
            return {"error": error_info}
        else:
            logger.error(f"Unhandled error in safe_execute: {e}")
            raise

def format_error_for_user(error_info: ErrorInfo) -> str:
    """Format error information for user display."""
    severity_emojis = {
        ErrorSeverity.LOW: "ℹ️",
        ErrorSeverity.MEDIUM: "⚠️",
        ErrorSeverity.HIGH: "❌",
        ErrorSeverity.CRITICAL: "🚨"
    }
    
    emoji = severity_emojis.get(error_info.severity, "❌")
    
    content = f"{emoji} **{error_info.user_message}**\n\n"
    
    if error_info.suggestions:
        content += "**💡 Suggestions:**\n"
        for suggestion in error_info.suggestions:
            content += f"- {suggestion}\n"
    
    content += f"\n*Error ID: {error_info.error_id}*"
    
    return content
