# Minimal Chainlit AWS Management Frontend Requirements
# Essential dependencies only to avoid resolution conflicts

# Core Chainlit Framework
chainlit==1.0.504

# HTTP client for backend communication
httpx==0.27.0

# Data processing
pandas==2.2.2
numpy==1.26.4

# Visualization
plotly==5.22.0

# File handling
openpyxl==3.1.5

# Async support
anyio==4.4.0

# Environment variables
python-dotenv==1.0.1
