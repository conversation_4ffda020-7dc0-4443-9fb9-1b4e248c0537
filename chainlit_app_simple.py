"""
Simple Chainlit AWS Management Frontend Application

A streamlined version compatible with the current Chainlit version.
"""

import chainlit as cl
import asyncio
import json
import uuid
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
import httpx
import os
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Configuration
AWS_AGENT_URL = os.getenv("AWS_AGENT_URL", "http://localhost:8001")
API_TIMEOUT = int(os.getenv("API_TIMEOUT", "120"))

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AWSAgentClient:
    """Simple client for communicating with the AWS Agent backend."""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.timeout = httpx.Timeout(API_TIMEOUT)
        
    async def health_check(self) -> Dict:
        """Check the health of the AWS Agent backend."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                response = await client.get(f"{self.base_url}/health")
                response.raise_for_status()
                return response.json()
            except Exception as e:
                logger.error(f"Health check failed: {e}")
                return {"status": "error", "error": str(e)}
    
    async def send_message(self, message: str, session_id: str) -> Dict:
        """Send a message to the AWS Agent and get response."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                payload = {
                    "message": message,
                    "session_id": session_id,
                    "stream": False
                }
                response = await client.post(f"{self.base_url}/chat", json=payload)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                logger.error(f"Message sending failed: {e}")
                return {"status": "error", "error": str(e)}

# Global AWS client
aws_client = AWSAgentClient(AWS_AGENT_URL)

@cl.on_chat_start
async def start():
    """Initialize the chat session when a user connects."""
    # Create session ID
    session_id = str(uuid.uuid4())
    cl.user_session.set("session_id", session_id)
    cl.user_session.set("message_count", 0)
    
    # Check backend health
    health_status = await aws_client.health_check()
    
    # Welcome message
    welcome_msg = f"""# 🚀 Welcome to AWS Management Assistant

I'm your AI-powered AWS management companion, ready to help you with:

## 🔧 **Core Capabilities**
- **EC2 Management**: Instance monitoring, cost analysis, optimization
- **S3 Operations**: Bucket management, storage optimization, cost tracking
- **RDS Administration**: Database monitoring, performance tuning
- **Lambda Functions**: Serverless cost analysis and optimization
- **Cost Analysis**: Comprehensive billing insights and recommendations

## 📊 **Available Commands**
Try asking me:
- "Show me my EC2 instances"
- "Analyze my AWS costs for this month"
- "List all S3 buckets"
- "Check RDS database status"
- "Generate a cost optimization report"
- "Show infrastructure health status"

## 🎯 **Quick Examples**
- `analyze costs for last 30 days`
- `list ec2 instances in us-east-1`
- `show s3 storage costs`
- `check security status`
- `optimize lambda functions`

---
**Backend Status**: {health_status.get('status', 'unknown').title()}
**Available Tools**: {health_status.get('available_tools', 0)}
**Active Servers**: {health_status.get('active_servers', 0)}

Ready to optimize your AWS infrastructure! 💪

**💡 Tip**: You can ask questions in natural language or use specific commands.
"""
    
    await cl.Message(content=welcome_msg).send()

@cl.on_message
async def main(message: cl.Message):
    """Handle incoming messages from users."""
    session_id = cl.user_session.get("session_id")
    message_count = cl.user_session.get("message_count", 0)
    
    # Update message count
    cl.user_session.set("message_count", message_count + 1)
    
    # Handle special commands
    if message.content.startswith("/"):
        await handle_special_command(message.content, session_id)
        return
    
    # Show processing message
    processing_msg = await cl.Message(content="🤖 Processing your request...").send()
    
    try:
        # Send message to AWS Agent backend
        response = await aws_client.send_message(message.content, session_id)
        
        if response.get("status") == "error":
            error_msg = f"❌ **Error**: {response.get('error', 'Unknown error occurred')}"
            await processing_msg.update(content=error_msg)
            return
        
        # Format and send the response
        formatted_response = format_aws_response(response)
        await processing_msg.update(content=formatted_response)
        
        # Add follow-up suggestions
        suggestions = get_follow_up_suggestions(message.content, response)
        if suggestions:
            suggestion_msg = "**💡 You might also want to try:**\n"
            for suggestion in suggestions:
                suggestion_msg += f"- {suggestion}\n"
            
            await cl.Message(content=suggestion_msg).send()
        
    except Exception as e:
        logger.error(f"Error processing message: {e}")
        error_content = f"❌ **System Error**: Failed to process your request.\n\n**Error Details**: {str(e)}"
        await processing_msg.update(content=error_content)

async def handle_special_command(command: str, session_id: str):
    """Handle special commands starting with /."""
    command = command.lower().strip()
    
    if command == "/help":
        help_content = """# ❓ Help - AWS Management Assistant

## 🎯 **Available Commands**

### **Cost Analysis**
- `analyze my AWS costs`
- `show cost breakdown by service`
- `compare costs between regions`
- `generate cost optimization report`

### **Resource Management**
- `list all EC2 instances`
- `show S3 buckets`
- `display RDS databases`
- `check Lambda functions`

### **Monitoring**
- `show infrastructure status`
- `check system health`
- `display performance metrics`
- `run security scan`

### **Special Commands**
- `/help` - Show this help message
- `/status` - Show system status
- `/examples` - Show example queries
- `/clear` - Clear conversation

## 💡 **Tips**
- Ask questions in natural language
- Be specific about services and regions
- Use time periods like "last 30 days" for cost analysis
- Mention specific resource IDs for detailed information

## 🆘 **Need Help?**
Just ask! I understand natural language queries about AWS resources.
"""
        await cl.Message(content=help_content).send()
        
    elif command == "/status":
        health_status = await aws_client.health_check()
        status_content = f"""# 🔍 System Status

## 🔧 **Backend Service**
- **Status**: {health_status.get('status', 'unknown').title()}
- **Available Tools**: {health_status.get('available_tools', 0)}
- **Active Servers**: {health_status.get('active_servers', 0)}

## 📊 **Session Information**
- **Session ID**: {session_id[:8]}...
- **Messages Sent**: {cl.user_session.get('message_count', 0)}
- **Backend URL**: {AWS_AGENT_URL}

## 🌐 **Connectivity**
- **API Timeout**: {API_TIMEOUT}s
- **Connection**: {'✅ Connected' if health_status.get('status') == 'healthy' else '❌ Disconnected'}

{'All systems operational! 🚀' if health_status.get('status') == 'healthy' else 'System issues detected. Please check backend service.'}
"""
        await cl.Message(content=status_content).send()
        
    elif command == "/examples":
        examples_content = """# 💡 Example Queries

## 💰 **Cost Analysis Examples**
```
"What are my AWS costs for this month?"
"Show me the most expensive services"
"Compare costs between us-east-1 and us-west-2"
"Analyze S3 storage costs"
"Find cost optimization opportunities"
```

## 🏗️ **Resource Management Examples**
```
"List all EC2 instances"
"Show me S3 buckets with public access"
"Display RDS database status"
"Find unused Lambda functions"
"Check EBS volumes"
```

## 📊 **Monitoring Examples**
```
"Show infrastructure health"
"Check system performance"
"Display security findings"
"Monitor resource utilization"
"Generate status report"
```

## 🔍 **Specific Queries**
```
"Show details for instance i-1234567890abcdef0"
"Analyze costs for EC2 service"
"List buckets in us-east-1 region"
"Check RDS performance metrics"
"Find Lambda functions with high costs"
```

Try any of these examples or ask your own questions!
"""
        await cl.Message(content=examples_content).send()
        
    elif command == "/clear":
        await cl.Message(content="🧹 Conversation cleared! How can I help you with AWS management?").send()
        
    else:
        await cl.Message(
            content=f"❓ Unknown command: {command}\n\nAvailable commands:\n"
                   "- `/help` - Show help\n"
                   "- `/status` - System status\n"
                   "- `/examples` - Example queries\n"
                   "- `/clear` - Clear conversation"
        ).send()

def format_aws_response(response: Dict[str, Any]) -> str:
    """Format AWS Agent response for better display."""
    content = response.get("response", "")
    tools_used = response.get("tools_used", [])
    
    # Add tool usage information if available
    if tools_used:
        tool_info = "\n\n---\n**🔧 Tools Used**: " + ", ".join(tools_used)
        content += tool_info
    
    # Add session information
    session_id = response.get("session_id", "")
    if session_id:
        content += f"\n\n*Session: {session_id[:8]}...*"
    
    return content

def get_follow_up_suggestions(user_message: str, response: Dict[str, Any]) -> List[str]:
    """Generate follow-up suggestions based on the conversation."""
    suggestions = []
    
    message_lower = user_message.lower()
    
    # Cost-related suggestions
    if any(word in message_lower for word in ["cost", "price", "billing", "spend"]):
        suggestions.extend([
            "Show cost optimization recommendations",
            "Compare costs with previous month",
            "Analyze cost trends over time"
        ])
    
    # EC2-related suggestions
    elif any(word in message_lower for word in ["ec2", "instance", "server"]):
        suggestions.extend([
            "Check EC2 utilization metrics",
            "Analyze EC2 cost optimization",
            "Show instance security status"
        ])
    
    # S3-related suggestions
    elif any(word in message_lower for word in ["s3", "bucket", "storage"]):
        suggestions.extend([
            "Analyze S3 storage costs",
            "Check bucket security settings",
            "Show storage optimization opportunities"
        ])
    
    # General suggestions
    else:
        suggestions.extend([
            "Show infrastructure overview",
            "Check system health status",
            "Analyze overall AWS costs"
        ])
    
    return suggestions[:3]  # Return top 3 suggestions

@cl.on_chat_end
async def end():
    """Handle chat session end."""
    logger.info("Chat session ended")

if __name__ == "__main__":
    # This will be handled by Chainlit's CLI
    pass
