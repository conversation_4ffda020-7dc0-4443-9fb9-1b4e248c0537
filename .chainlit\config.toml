[project]
# Whether to enable telemetry (default: true). No personal data is collected.
enable_telemetry = true

# List of environment variables to be provided by each user to use the app.
user_env = []

# Duration (in seconds) during which the session is saved when the connection is lost
session_timeout = 3600

# Enable third parties caching (e.g <PERSON><PERSON>hain cache)
cache = false

# Authorized origins
allow_origins = ["*"]

# Follow symlink for asset mount (see https://github.com/Chainlit/chainlit/issues/317)
# follow_symlink = false

[features]
# Show the prompt playground
prompt_playground = true

# Process and display HTML in messages. This can be a security risk (see https://stackoverflow.com/questions/19603097/why-is-it-dangerous-to-render-user-generated-html-or-javascript)
unsafe_allow_html = false

# Process and display mathematical expressions. This can clash with "$" characters in messages.
latex = false

# Automatically tag threads when a user takes an action (useful for analytics)
auto_tag_thread = true

# Literal values to use for auto-tagging threads
auto_tag_thread_values = ["aws-query", "cost-analysis", "resource-management", "monitoring", "security", "file-upload", "export"]

[UI]
# Name of the app and chatbot.
name = "AWS Management Assistant"

# Show the readme while the thread is empty.
show_readme_as_default = true

# Description of the app and chatbot. This is used for HTML tags.
description = "AI-powered AWS management companion for infrastructure optimization, cost analysis, and resource monitoring."

# Large size content are by default collapsed for a cleaner ui
default_collapse_content = true

# The default value for the expand messages settings.
default_expand_messages = false

# Hide the chain of thought details from the user in the UI.
hide_cot = false

# Link to your github repo. This will add a github button in the UI's header.
# github = ""

# Specify a CSS file that can be used to customize the user interface.
# The CSS file can be served from the public directory or via an external link.
# custom_css = "/public/style.css"

# Specify a Javascript file that can be used to customize the user interface.
# The Javascript file can be served from the public directory.
# custom_js = "/public/script.js"

# Specify a custom font url.
# custom_font = "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap"

# Specify a custom build directory for the UI. This is used to serve the UI from a different location.
# custom_build = "./public/build"

# Override default MUI light theme. (Check theme.ts)
[UI.theme]
primary_color = "#FF9900"
background_color = "#FAFAFA"
paper_color = "#FFFFFF"

[UI.theme.light]
background = "#FAFAFA"
paper = "#FFFFFF"

    [UI.theme.light.primary]
    main = "#FF9900"
    dark = "#E68900"
    light = "#FFB84D"

    [UI.theme.light.secondary]
    main = "#232F3E"
    dark = "#1A252F"
    light = "#3D4A5C"

    [UI.theme.light.text]
    primary = "#232F3E"
    secondary = "#5A6C7D"

[UI.theme.dark]
background = "#232F3E"
paper = "#2D3A4B"

    [UI.theme.dark.primary]
    main = "#FF9900"
    dark = "#E68900"
    light = "#FFB84D"

    [UI.theme.dark.secondary]
    main = "#FFFFFF"
    dark = "#E0E0E0"
    light = "#F5F5F5"

    [UI.theme.dark.text]
    primary = "#FFFFFF"
    secondary = "#B0BEC5"

[meta]
generated_by = "1.0.0"
