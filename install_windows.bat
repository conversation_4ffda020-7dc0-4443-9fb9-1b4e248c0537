@echo off
echo AWS Management Assistant - Windows Installation
echo ===============================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo Python found, proceeding with installation...

REM Upgrade pip first
echo.
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install core dependencies
echo.
echo Installing core dependencies...
python -m pip install setuptools wheel

REM Install backend dependencies
echo.
echo Installing backend dependencies...
python -m pip install -r requirements.txt

REM Install minimal Chainlit dependencies
echo.
echo Installing Chainlit and essential dependencies...
python -m pip install chainlit==1.0.504
python -m pip install httpx==0.27.0
python -m pip install pandas==2.2.2
python -m pip install numpy==1.26.4
python -m pip install plotly==5.22.0
python -m pip install python-dotenv==1.0.1

REM Install optional dependencies (continue on failure)
echo.
echo Installing optional dependencies...
python -m pip install openpyxl==3.1.5 2>nul
python -m pip install xlsxwriter==3.1.9 2>nul
python -m pip install structlog==24.1.0 2>nul
python -m pip install prometheus-client==0.20.0 2>nul

REM Create necessary directories
echo.
echo Creating directories...
if not exist "logs" mkdir logs
if not exist "components" mkdir components

REM Test installation
echo.
echo Testing installation...
python -c "import chainlit; print('Chainlit version:', chainlit.__version__)" 2>nul
if errorlevel 1 (
    echo WARNING: Chainlit import test failed
) else (
    echo Chainlit installation verified
)

python -c "import httpx; print('httpx version:', httpx.__version__)" 2>nul
if errorlevel 1 (
    echo WARNING: httpx import test failed
) else (
    echo httpx installation verified
)

echo.
echo ===============================================
echo Installation completed!
echo.
echo Next steps:
echo 1. Configure environment variables in .env file
echo 2. Start AWS Agent: python aws_agent.py
echo 3. Start Chainlit: chainlit run chainlit_app.py
echo 4. Open browser: http://localhost:8000
echo.
echo If you encounter issues, try:
echo - pip install -r chainlit_requirements_minimal.txt
echo - python install_dependencies.py
echo ===============================================
pause
