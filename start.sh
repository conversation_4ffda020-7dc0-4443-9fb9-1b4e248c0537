#!/bin/bash

# AWS Management Assistant Startup Script
# This script starts both the AWS Agent backend and Chainlit frontend

set -e

# Configuration
BACKEND_PORT=8001
FRONTEND_PORT=8000
LOG_DIR="logs"
BACKEND_LOG="$LOG_DIR/aws-agent.log"
FRONTEND_LOG="$LOG_DIR/chainlit.log"
PID_DIR="pids"
BACKEND_PID="$PID_DIR/aws-agent.pid"
FRONTEND_PID="$PID_DIR/chainlit.pid"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    mkdir -p "$LOG_DIR" "$PID_DIR"
}

# Check if ports are available
check_ports() {
    log "Checking port availability..."
    
    if lsof -Pi :$BACKEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        error "Port $BACKEND_PORT is already in use"
        exit 1
    fi
    
    if lsof -Pi :$FRONTEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        error "Port $FRONTEND_PORT is already in use"
        exit 1
    fi
    
    success "Ports $BACKEND_PORT and $FRONTEND_PORT are available"
}

# Check dependencies
check_dependencies() {
    log "Checking dependencies..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        error "Python 3 is not installed"
        exit 1
    fi
    
    # Check pip packages
    if ! python3 -c "import fastapi, uvicorn, chainlit" 2>/dev/null; then
        error "Required Python packages are not installed"
        echo "Please run: pip install -r requirements.txt && pip install -r chainlit_requirements.txt"
        exit 1
    fi
    
    # Check configuration files
    if [[ ! -f "aws_agent.py" ]]; then
        error "aws_agent.py not found"
        exit 1
    fi
    
    if [[ ! -f "chainlit_app.py" ]]; then
        error "chainlit_app.py not found"
        exit 1
    fi
    
    if [[ ! -f "servers_config.json" ]]; then
        error "servers_config.json not found"
        exit 1
    fi
    
    success "All dependencies are available"
}

# Start AWS Agent Backend
start_backend() {
    log "Starting AWS Agent Backend on port $BACKEND_PORT..."
    
    nohup python3 aws_agent.py > "$BACKEND_LOG" 2>&1 &
    echo $! > "$BACKEND_PID"
    
    # Wait for backend to start
    local retries=0
    local max_retries=30
    
    while [[ $retries -lt $max_retries ]]; do
        if curl -s -f "http://localhost:$BACKEND_PORT/health" >/dev/null 2>&1; then
            success "AWS Agent Backend started successfully"
            return 0
        fi
        
        sleep 2
        ((retries++))
        log "Waiting for backend to start... ($retries/$max_retries)"
    done
    
    error "Failed to start AWS Agent Backend"
    cat "$BACKEND_LOG"
    exit 1
}

# Start Chainlit Frontend
start_frontend() {
    log "Starting Chainlit Frontend on port $FRONTEND_PORT..."
    
    nohup chainlit run chainlit_app.py --host 0.0.0.0 --port $FRONTEND_PORT > "$FRONTEND_LOG" 2>&1 &
    echo $! > "$FRONTEND_PID"
    
    # Wait for frontend to start
    local retries=0
    local max_retries=30
    
    while [[ $retries -lt $max_retries ]]; do
        if curl -s -f "http://localhost:$FRONTEND_PORT" >/dev/null 2>&1; then
            success "Chainlit Frontend started successfully"
            return 0
        fi
        
        sleep 2
        ((retries++))
        log "Waiting for frontend to start... ($retries/$max_retries)"
    done
    
    error "Failed to start Chainlit Frontend"
    cat "$FRONTEND_LOG"
    exit 1
}

# Stop services
stop_services() {
    log "Stopping services..."
    
    # Stop frontend
    if [[ -f "$FRONTEND_PID" ]]; then
        local frontend_pid=$(cat "$FRONTEND_PID")
        if kill -0 "$frontend_pid" 2>/dev/null; then
            log "Stopping Chainlit Frontend (PID: $frontend_pid)"
            kill "$frontend_pid"
            rm -f "$FRONTEND_PID"
        fi
    fi
    
    # Stop backend
    if [[ -f "$BACKEND_PID" ]]; then
        local backend_pid=$(cat "$BACKEND_PID")
        if kill -0 "$backend_pid" 2>/dev/null; then
            log "Stopping AWS Agent Backend (PID: $backend_pid)"
            kill "$backend_pid"
            rm -f "$BACKEND_PID"
        fi
    fi
    
    success "Services stopped"
}

# Check service status
check_status() {
    log "Checking service status..."
    
    # Check backend
    if [[ -f "$BACKEND_PID" ]]; then
        local backend_pid=$(cat "$BACKEND_PID")
        if kill -0 "$backend_pid" 2>/dev/null; then
            success "AWS Agent Backend is running (PID: $backend_pid)"
        else
            warning "AWS Agent Backend PID file exists but process is not running"
            rm -f "$BACKEND_PID"
        fi
    else
        warning "AWS Agent Backend is not running"
    fi
    
    # Check frontend
    if [[ -f "$FRONTEND_PID" ]]; then
        local frontend_pid=$(cat "$FRONTEND_PID")
        if kill -0 "$frontend_pid" 2>/dev/null; then
            success "Chainlit Frontend is running (PID: $frontend_pid)"
        else
            warning "Chainlit Frontend PID file exists but process is not running"
            rm -f "$FRONTEND_PID"
        fi
    else
        warning "Chainlit Frontend is not running"
    fi
    
    # Check port connectivity
    if curl -s -f "http://localhost:$BACKEND_PORT/health" >/dev/null 2>&1; then
        success "Backend health check passed"
    else
        warning "Backend health check failed"
    fi
    
    if curl -s -f "http://localhost:$FRONTEND_PORT" >/dev/null 2>&1; then
        success "Frontend connectivity check passed"
    else
        warning "Frontend connectivity check failed"
    fi
}

# Show logs
show_logs() {
    local service="$1"
    
    case "$service" in
        "backend"|"aws-agent")
            if [[ -f "$BACKEND_LOG" ]]; then
                tail -f "$BACKEND_LOG"
            else
                error "Backend log file not found"
            fi
            ;;
        "frontend"|"chainlit")
            if [[ -f "$FRONTEND_LOG" ]]; then
                tail -f "$FRONTEND_LOG"
            else
                error "Frontend log file not found"
            fi
            ;;
        "all"|"")
            if [[ -f "$BACKEND_LOG" ]] && [[ -f "$FRONTEND_LOG" ]]; then
                tail -f "$BACKEND_LOG" "$FRONTEND_LOG"
            else
                error "Log files not found"
            fi
            ;;
        *)
            error "Unknown service: $service"
            echo "Available services: backend, frontend, all"
            ;;
    esac
}

# Show usage
show_usage() {
    echo "AWS Management Assistant Control Script"
    echo ""
    echo "Usage: $0 {start|stop|restart|status|logs [service]}"
    echo ""
    echo "Commands:"
    echo "  start    - Start both backend and frontend services"
    echo "  stop     - Stop both services"
    echo "  restart  - Restart both services"
    echo "  status   - Check service status"
    echo "  logs     - Show logs (optional: backend, frontend, all)"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 logs backend"
    echo "  $0 status"
}

# Cleanup on exit
cleanup() {
    log "Cleaning up..."
    stop_services
}

# Set trap for cleanup
trap cleanup EXIT INT TERM

# Main script logic
case "$1" in
    "start")
        create_directories
        check_dependencies
        check_ports
        start_backend
        start_frontend
        
        success "AWS Management Assistant started successfully!"
        echo ""
        echo "🚀 Services are now running:"
        echo "   Frontend: http://localhost:$FRONTEND_PORT"
        echo "   Backend:  http://localhost:$BACKEND_PORT"
        echo ""
        echo "📝 Logs:"
        echo "   Backend:  $BACKEND_LOG"
        echo "   Frontend: $FRONTEND_LOG"
        echo ""
        echo "🛑 To stop services: $0 stop"
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        stop_services
        sleep 2
        create_directories
        check_dependencies
        check_ports
        start_backend
        start_frontend
        success "Services restarted successfully!"
        ;;
    "status")
        check_status
        ;;
    "logs")
        show_logs "$2"
        ;;
    *)
        show_usage
        exit 1
        ;;
esac
