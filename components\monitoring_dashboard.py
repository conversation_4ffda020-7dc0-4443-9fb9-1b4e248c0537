"""
Comprehensive Monitoring Dashboard for AWS Resources

This module provides real-time monitoring capabilities, cost analysis,
and infrastructure health indicators for AWS resources.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import chainlit as cl
from components.aws_agent_integration import AWSAgentClient
from components.aws_visualizations import AWSVisualizationManager
from components.aws_resource_display import ResourceDisplayFormatter

class MonitoringDashboard:
    """Comprehensive monitoring dashboard for AWS resources."""
    
    def __init__(self, aws_client: AWSAgentClient):
        self.aws_client = aws_client
        self.viz_manager = AWSVisualizationManager()
        self.formatter = ResourceDisplayFormatter()
        self.cache = {}
        self.cache_ttl = 300  # 5 minutes
        
    async def show_infrastructure_overview(self, session_id: str) -> str:
        """Display comprehensive infrastructure overview."""
        try:
            # Get infrastructure data
            infra_data = await self._get_infrastructure_data(session_id)
            
            overview = "# 🏗️ Infrastructure Overview\n\n"
            
            # Service summary cards
            overview += "## 📊 Service Summary\n\n"
            
            services = infra_data.get('services', {})
            for service_name, metrics in services.items():
                overview += self._create_service_card(service_name, metrics)
                overview += "\n"
            
            # Resource counts
            overview += "## 📈 Resource Counts\n\n"
            resource_counts = infra_data.get('resource_counts', {})
            
            for resource_type, count in resource_counts.items():
                status_emoji = "🟢" if count > 0 else "⚪"
                overview += f"- {status_emoji} **{resource_type}**: {count}\n"
            
            # Health status
            overview += "\n## 🔍 Health Status\n\n"
            health_data = infra_data.get('health', {})
            
            overall_health = health_data.get('overall', 'unknown')
            health_emoji = "🟢" if overall_health == 'healthy' else "🔴" if overall_health == 'unhealthy' else "🟡"
            
            overview += f"**Overall Health**: {health_emoji} {overall_health.title()}\n\n"
            
            # Service health details
            service_health = health_data.get('services', {})
            for service, status in service_health.items():
                status_emoji = "🟢" if status == 'healthy' else "🔴" if status == 'unhealthy' else "🟡"
                overview += f"- {status_emoji} **{service}**: {status.title()}\n"
            
            # Recent alerts
            alerts = infra_data.get('alerts', [])
            if alerts:
                overview += "\n## 🚨 Recent Alerts\n\n"
                for alert in alerts[:5]:  # Show last 5 alerts
                    severity_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(alert.get('severity', 'low'), "🟡")
                    overview += f"- {severity_emoji} **{alert.get('title', 'Alert')}** ({alert.get('timestamp', 'Unknown time')})\n"
            
            return overview
            
        except Exception as e:
            return f"❌ Failed to load infrastructure overview: {str(e)}"
    
    async def show_cost_dashboard(self, session_id: str, time_period: str = "30d") -> str:
        """Display comprehensive cost analysis dashboard."""
        try:
            # Get cost data
            cost_data = await self._get_cost_data(session_id, time_period)
            
            dashboard = "# 💰 Cost Analysis Dashboard\n\n"
            
            # Total cost summary
            total_cost = cost_data.get('total_cost', 0)
            previous_cost = cost_data.get('previous_period_cost', 0)
            
            dashboard += f"## 📊 Cost Summary ({time_period})\n\n"
            dashboard += f"**Total Cost**: ${total_cost:.2f}\n"
            
            if previous_cost > 0:
                change = ((total_cost - previous_cost) / previous_cost) * 100
                trend_emoji = "📈" if change > 0 else "📉" if change < 0 else "➡️"
                dashboard += f"**Change from Previous Period**: {trend_emoji} {change:+.1f}%\n"
            
            dashboard += "\n"
            
            # Top spending services
            service_costs = cost_data.get('service_breakdown', {})
            if service_costs:
                dashboard += "## 🔧 Top Spending Services\n\n"
                sorted_services = sorted(service_costs.items(), key=lambda x: x[1], reverse=True)
                
                for service, cost in sorted_services[:5]:
                    percentage = (cost / total_cost) * 100 if total_cost > 0 else 0
                    dashboard += f"- **{service}**: ${cost:.2f} ({percentage:.1f}%)\n"
                
                dashboard += "\n"
            
            # Regional breakdown
            regional_costs = cost_data.get('regional_breakdown', {})
            if regional_costs:
                dashboard += "## 🌍 Cost by Region\n\n"
                sorted_regions = sorted(regional_costs.items(), key=lambda x: x[1], reverse=True)
                
                for region, cost in sorted_regions[:5]:
                    percentage = (cost / total_cost) * 100 if total_cost > 0 else 0
                    dashboard += f"- **{region}**: ${cost:.2f} ({percentage:.1f}%)\n"
                
                dashboard += "\n"
            
            # Cost optimization opportunities
            optimizations = cost_data.get('optimization_opportunities', [])
            if optimizations:
                dashboard += "## 🎯 Cost Optimization Opportunities\n\n"
                total_savings = sum(opt.get('potential_savings', 0) for opt in optimizations)
                dashboard += f"**Total Potential Savings**: ${total_savings:.2f}/month\n\n"
                
                for opt in optimizations[:3]:  # Show top 3 opportunities
                    priority_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(opt.get('priority', 'medium'), "🟡")
                    dashboard += f"- {priority_emoji} **{opt.get('title', 'Optimization')}**: ${opt.get('potential_savings', 0):.2f}/month\n"
            
            return dashboard
            
        except Exception as e:
            return f"❌ Failed to load cost dashboard: {str(e)}"
    
    async def show_performance_metrics(self, session_id: str) -> str:
        """Display performance metrics dashboard."""
        try:
            # Get performance data
            perf_data = await self._get_performance_data(session_id)
            
            metrics = "# 📊 Performance Metrics Dashboard\n\n"
            
            # Overall performance score
            overall_score = perf_data.get('overall_score', 0)
            score_emoji = "🟢" if overall_score >= 80 else "🟡" if overall_score >= 60 else "🔴"
            
            metrics += f"## {score_emoji} Overall Performance Score: {overall_score}/100\n\n"
            
            # Service performance breakdown
            service_metrics = perf_data.get('service_metrics', {})
            if service_metrics:
                metrics += "## 🔧 Service Performance\n\n"
                
                for service, data in service_metrics.items():
                    response_time = data.get('avg_response_time', 0)
                    error_rate = data.get('error_rate', 0)
                    throughput = data.get('throughput', 0)
                    
                    # Performance indicators
                    rt_emoji = "🟢" if response_time < 200 else "🟡" if response_time < 500 else "🔴"
                    er_emoji = "🟢" if error_rate < 1 else "🟡" if error_rate < 5 else "🔴"
                    
                    metrics += f"### {service}\n"
                    metrics += f"- {rt_emoji} **Response Time**: {response_time:.0f}ms\n"
                    metrics += f"- {er_emoji} **Error Rate**: {error_rate:.2f}%\n"
                    metrics += f"- **Throughput**: {throughput:.0f} req/min\n\n"
            
            # Resource utilization
            utilization = perf_data.get('resource_utilization', {})
            if utilization:
                metrics += "## 💻 Resource Utilization\n\n"
                
                cpu_avg = utilization.get('cpu_average', 0)
                memory_avg = utilization.get('memory_average', 0)
                disk_avg = utilization.get('disk_average', 0)
                
                cpu_emoji = "🟢" if cpu_avg < 70 else "🟡" if cpu_avg < 85 else "🔴"
                mem_emoji = "🟢" if memory_avg < 80 else "🟡" if memory_avg < 90 else "🔴"
                disk_emoji = "🟢" if disk_avg < 80 else "🟡" if disk_avg < 90 else "🔴"
                
                metrics += f"- {cpu_emoji} **CPU Average**: {cpu_avg:.1f}%\n"
                metrics += f"- {mem_emoji} **Memory Average**: {memory_avg:.1f}%\n"
                metrics += f"- {disk_emoji} **Disk Average**: {disk_avg:.1f}%\n\n"
            
            # Performance recommendations
            recommendations = perf_data.get('recommendations', [])
            if recommendations:
                metrics += "## 🎯 Performance Recommendations\n\n"
                
                for rec in recommendations[:3]:
                    impact_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(rec.get('impact', 'medium'), "🟡")
                    metrics += f"- {impact_emoji} **{rec.get('title', 'Recommendation')}**\n"
                    metrics += f"  {rec.get('description', 'No description available')}\n\n"
            
            return metrics
            
        except Exception as e:
            return f"❌ Failed to load performance metrics: {str(e)}"
    
    async def show_security_dashboard(self, session_id: str) -> str:
        """Display security and compliance dashboard."""
        try:
            # Get security data
            security_data = await self._get_security_data(session_id)
            
            dashboard = "# 🔒 Security & Compliance Dashboard\n\n"
            
            # Security score
            security_score = security_data.get('security_score', 0)
            score_emoji = "🟢" if security_score >= 80 else "🟡" if security_score >= 60 else "🔴"
            
            dashboard += f"## {score_emoji} Security Score: {security_score}/100\n\n"
            
            # Security findings
            findings = security_data.get('findings', [])
            if findings:
                dashboard += "## 🚨 Security Findings\n\n"
                
                # Group by severity
                high_findings = [f for f in findings if f.get('severity') == 'high']
                medium_findings = [f for f in findings if f.get('severity') == 'medium']
                low_findings = [f for f in findings if f.get('severity') == 'low']
                
                dashboard += f"- 🔴 **High Severity**: {len(high_findings)}\n"
                dashboard += f"- 🟡 **Medium Severity**: {len(medium_findings)}\n"
                dashboard += f"- 🟢 **Low Severity**: {len(low_findings)}\n\n"
                
                # Show top high severity findings
                if high_findings:
                    dashboard += "### 🔴 Critical Security Issues\n\n"
                    for finding in high_findings[:3]:
                        dashboard += f"- **{finding.get('title', 'Security Issue')}**\n"
                        dashboard += f"  Resource: {finding.get('resource', 'Unknown')}\n"
                        dashboard += f"  Description: {finding.get('description', 'No description')}\n\n"
            
            # Compliance status
            compliance = security_data.get('compliance', {})
            if compliance:
                dashboard += "## 📋 Compliance Status\n\n"
                
                for framework, status in compliance.items():
                    compliance_emoji = "✅" if status.get('compliant', False) else "❌"
                    score = status.get('score', 0)
                    dashboard += f"- {compliance_emoji} **{framework}**: {score}% compliant\n"
                
                dashboard += "\n"
            
            # Security recommendations
            sec_recommendations = security_data.get('recommendations', [])
            if sec_recommendations:
                dashboard += "## 🛡️ Security Recommendations\n\n"
                
                for rec in sec_recommendations[:5]:
                    priority_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(rec.get('priority', 'medium'), "🟡")
                    dashboard += f"- {priority_emoji} **{rec.get('title', 'Security Recommendation')}**\n"
                    dashboard += f"  {rec.get('description', 'No description available')}\n\n"
            
            return dashboard
            
        except Exception as e:
            return f"❌ Failed to load security dashboard: {str(e)}"
    
    def _create_service_card(self, service_name: str, metrics: Dict[str, Any]) -> str:
        """Create a service summary card."""
        status = metrics.get('status', 'unknown')
        status_emoji = "🟢" if status == 'healthy' else "🔴" if status == 'unhealthy' else "🟡"
        
        card = f"""
**{status_emoji} {service_name}**
- Resources: {metrics.get('resource_count', 0)}
- Monthly Cost: ${metrics.get('monthly_cost', 0):.2f}
- Health: {status.title()}
"""
        return card
    
    async def _get_infrastructure_data(self, session_id: str) -> Dict[str, Any]:
        """Get infrastructure data from AWS agent."""
        cache_key = f"infra_{session_id}"
        
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key]['data']
        
        try:
            response = await self.aws_client.send_message(
                "Get infrastructure overview with resource counts and health status",
                session_id
            )
            
            # Parse response for infrastructure data
            # This would be customized based on your AWS agent's response format
            data = self._parse_infrastructure_response(response)
            
            self._cache_data(cache_key, data)
            return data
            
        except Exception as e:
            # Return mock data for demonstration
            return self._get_mock_infrastructure_data()
    
    async def _get_cost_data(self, session_id: str, time_period: str) -> Dict[str, Any]:
        """Get cost data from AWS agent."""
        cache_key = f"cost_{session_id}_{time_period}"
        
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key]['data']
        
        try:
            response = await self.aws_client.send_message(
                f"Get cost analysis for {time_period} with service and regional breakdown",
                session_id
            )
            
            data = self._parse_cost_response(response)
            self._cache_data(cache_key, data)
            return data
            
        except Exception as e:
            return self._get_mock_cost_data()
    
    async def _get_performance_data(self, session_id: str) -> Dict[str, Any]:
        """Get performance data from AWS agent."""
        cache_key = f"perf_{session_id}"
        
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key]['data']
        
        try:
            response = await self.aws_client.send_message(
                "Get performance metrics and resource utilization data",
                session_id
            )
            
            data = self._parse_performance_response(response)
            self._cache_data(cache_key, data)
            return data
            
        except Exception as e:
            return self._get_mock_performance_data()
    
    async def _get_security_data(self, session_id: str) -> Dict[str, Any]:
        """Get security data from AWS agent."""
        cache_key = f"security_{session_id}"
        
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key]['data']
        
        try:
            response = await self.aws_client.send_message(
                "Get security findings and compliance status",
                session_id
            )
            
            data = self._parse_security_response(response)
            self._cache_data(cache_key, data)
            return data
            
        except Exception as e:
            return self._get_mock_security_data()
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid."""
        if cache_key not in self.cache:
            return False
        
        cache_time = self.cache[cache_key]['timestamp']
        return (datetime.now() - cache_time).seconds < self.cache_ttl
    
    def _cache_data(self, cache_key: str, data: Dict[str, Any]):
        """Cache data with timestamp."""
        self.cache[cache_key] = {
            'data': data,
            'timestamp': datetime.now()
        }
    
    def _parse_infrastructure_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Parse infrastructure response from AWS agent."""
        # This would be customized based on your AWS agent's response format
        return response.get('infrastructure_data', self._get_mock_infrastructure_data())
    
    def _parse_cost_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Parse cost response from AWS agent."""
        return response.get('cost_data', self._get_mock_cost_data())
    
    def _parse_performance_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Parse performance response from AWS agent."""
        return response.get('performance_data', self._get_mock_performance_data())
    
    def _parse_security_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Parse security response from AWS agent."""
        return response.get('security_data', self._get_mock_security_data())
    
    def _get_mock_infrastructure_data(self) -> Dict[str, Any]:
        """Get mock infrastructure data for demonstration."""
        return {
            'services': {
                'EC2': {'resource_count': 12, 'monthly_cost': 1250.50, 'status': 'healthy'},
                'S3': {'resource_count': 8, 'monthly_cost': 89.25, 'status': 'healthy'},
                'RDS': {'resource_count': 3, 'monthly_cost': 456.75, 'status': 'healthy'},
                'Lambda': {'resource_count': 25, 'monthly_cost': 23.10, 'status': 'healthy'}
            },
            'resource_counts': {
                'EC2 Instances': 12,
                'S3 Buckets': 8,
                'RDS Instances': 3,
                'Lambda Functions': 25,
                'Load Balancers': 2
            },
            'health': {
                'overall': 'healthy',
                'services': {
                    'EC2': 'healthy',
                    'S3': 'healthy',
                    'RDS': 'healthy',
                    'Lambda': 'healthy'
                }
            },
            'alerts': []
        }
    
    def _get_mock_cost_data(self) -> Dict[str, Any]:
        """Get mock cost data for demonstration."""
        return {
            'total_cost': 1819.60,
            'previous_period_cost': 1654.30,
            'service_breakdown': {
                'EC2': 1250.50,
                'RDS': 456.75,
                'S3': 89.25,
                'CloudWatch': 45.30,
                'Lambda': 23.10
            },
            'regional_breakdown': {
                'us-east-1': 1200.40,
                'us-west-2': 419.20,
                'eu-west-1': 200.00
            },
            'optimization_opportunities': [
                {
                    'title': 'Right-size EC2 instances',
                    'potential_savings': 245.50,
                    'priority': 'high'
                },
                {
                    'title': 'Use Reserved Instances',
                    'potential_savings': 187.25,
                    'priority': 'medium'
                }
            ]
        }
    
    def _get_mock_performance_data(self) -> Dict[str, Any]:
        """Get mock performance data for demonstration."""
        return {
            'overall_score': 85,
            'service_metrics': {
                'EC2': {'avg_response_time': 150, 'error_rate': 0.5, 'throughput': 1200},
                'RDS': {'avg_response_time': 45, 'error_rate': 0.1, 'throughput': 800},
                'Lambda': {'avg_response_time': 250, 'error_rate': 1.2, 'throughput': 2500}
            },
            'resource_utilization': {
                'cpu_average': 65.5,
                'memory_average': 72.3,
                'disk_average': 45.8
            },
            'recommendations': [
                {
                    'title': 'Optimize Lambda memory allocation',
                    'description': 'Several Lambda functions are over-provisioned',
                    'impact': 'medium'
                }
            ]
        }
    
    def _get_mock_security_data(self) -> Dict[str, Any]:
        """Get mock security data for demonstration."""
        return {
            'security_score': 78,
            'findings': [
                {
                    'title': 'S3 bucket with public read access',
                    'severity': 'high',
                    'resource': 'my-public-bucket',
                    'description': 'Bucket allows public read access'
                }
            ],
            'compliance': {
                'SOC 2': {'compliant': True, 'score': 95},
                'PCI DSS': {'compliant': False, 'score': 72}
            },
            'recommendations': [
                {
                    'title': 'Enable MFA for all IAM users',
                    'description': 'Multi-factor authentication should be enabled',
                    'priority': 'high'
                }
            ]
        }
