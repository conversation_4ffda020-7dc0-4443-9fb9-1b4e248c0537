"""
Chainlit AWS Management Frontend Application

A comprehensive frontend for AWS management operations with real-time chat interface,
monitoring dashboards, and resource visualization capabilities.
"""

import chainlit as cl
import asyncio
import json
import uuid
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import httpx
import os
from dotenv import load_dotenv
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from pathlib import Path
import logging

# Import custom components
from components.aws_agent_integration import AWSAgentClient, ConversationManager, ResponseFormatter
from components.aws_visualizations import AWSVisualizationManager
from components.aws_resource_display import ResourceDisplayFormatter
from components.monitoring_dashboard import MonitoringDashboard
from components.file_operations import FileProcessor, ReportExporter, get_file_icon, format_file_size
from components.user_experience import CommandSuggestionEngine, NavigationHelper, SearchEngine, HelpSystem
from components.error_handling import ErrorHandler, LoadingStateManager, ResponsiveDesignHelper, safe_execute, format_error_for_user

# Load environment variables
load_dotenv()

# Configuration
AWS_AGENT_URL = os.getenv("AWS_AGENT_URL", "http://localhost:8001")
API_TIMEOUT = int(os.getenv("API_TIMEOUT", "120"))
MAX_FILE_SIZE = int(os.getenv("MAX_FILE_SIZE", "10485760"))  # 10MB

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global state for session management
user_sessions: Dict[str, Dict] = {}
aws_client = None
monitoring_dashboard = None
file_processor = None
report_exporter = None
command_engine = None
navigation_helper = None
search_engine = None
help_system = None
error_handler = None
loading_manager = None

class AWSAgentClient:
    """Client for communicating with the AWS Agent backend."""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.timeout = httpx.Timeout(API_TIMEOUT)
        
    async def health_check(self) -> Dict:
        """Check the health of the AWS Agent backend."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                response = await client.get(f"{self.base_url}/health")
                response.raise_for_status()
                return response.json()
            except Exception as e:
                logger.error(f"Health check failed: {e}")
                return {"status": "error", "error": str(e)}
    
    async def send_message(self, message: str, session_id: str) -> Dict:
        """Send a message to the AWS Agent and get response."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                payload = {
                    "message": message,
                    "session_id": session_id,
                    "stream": False
                }
                response = await client.post(f"{self.base_url}/chat", json=payload)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                logger.error(f"Message sending failed: {e}")
                return {"status": "error", "error": str(e)}
    
    async def get_session_stats(self, session_id: str) -> Dict:
        """Get session statistics and metrics."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                response = await client.get(f"{self.base_url}/sessions/{session_id}/stats")
                if response.status_code == 200:
                    return response.json()
                return {"tools_used": 0, "messages": 0, "cost_estimate": 0.0}
            except Exception as e:
                logger.error(f"Session stats failed: {e}")
                return {"tools_used": 0, "messages": 0, "cost_estimate": 0.0}

class SessionManager:
    """Manage user sessions and conversation state."""
    
    def __init__(self):
        self.sessions = {}
    
    def get_or_create_session(self, user_id: str) -> Dict:
        """Get existing session or create new one."""
        if user_id not in self.sessions:
            self.sessions[user_id] = {
                "session_id": str(uuid.uuid4()),
                "created_at": datetime.now(),
                "last_activity": datetime.now(),
                "message_count": 0,
                "tools_used": [],
                "preferences": {},
                "conversation_history": []
            }
        return self.sessions[user_id]
    
    def update_session_activity(self, user_id: str):
        """Update last activity timestamp."""
        if user_id in self.sessions:
            self.sessions[user_id]["last_activity"] = datetime.now()
            self.sessions[user_id]["message_count"] += 1

# Initialize global components
session_manager = SessionManager()

@cl.on_chat_start
async def start():
    """Initialize the chat session when a user connects."""
    # Get or create user session
    user_id = cl.user_session.get("id", str(uuid.uuid4()))
    cl.user_session.set("user_id", user_id)
    
    session_data = session_manager.get_or_create_session(user_id)
    cl.user_session.set("session_data", session_data)
    
    # Initialize global components
    global aws_client, monitoring_dashboard, file_processor, report_exporter
    global command_engine, navigation_helper, search_engine, help_system
    global error_handler, loading_manager

    # Initialize error handling first
    error_handler = ErrorHandler()
    loading_manager = LoadingStateManager()

    # Initialize other components with error handling
    try:
        aws_client = AWSAgentClient(AWS_AGENT_URL)
        monitoring_dashboard = MonitoringDashboard(aws_client)
        file_processor = FileProcessor()
        report_exporter = ReportExporter()
        command_engine = CommandSuggestionEngine()
        navigation_helper = NavigationHelper()
        search_engine = SearchEngine()
        help_system = HelpSystem()
    except Exception as e:
        error_info = await error_handler.handle_error(e, {"context": "initialization"})
        logger.error(f"Initialization failed: {error_info.error_id}")
    
    # Check backend health
    health_status = await aws_client.health_check()
    
    # Welcome message with system status
    welcome_msg = f"""# 🚀 Welcome to AWS Management Assistant

I'm your AI-powered AWS management companion, ready to help you with:

## 🔧 **Core Capabilities**
- **EC2 Management**: Instance monitoring, cost analysis, optimization
- **S3 Operations**: Bucket management, storage optimization, cost tracking
- **RDS Administration**: Database monitoring, performance tuning
- **Lambda Functions**: Serverless cost analysis and optimization
- **Cost Analysis**: Comprehensive billing insights and recommendations

## 📊 **Advanced Features**
- Real-time AWS resource monitoring
- Interactive cost visualization dashboards
- Infrastructure health status tracking
- Automated optimization recommendations
- File upload for configurations and scripts
- Export capabilities for reports and data

## 🎯 **Quick Start Examples**
Try asking me:
- "Show me my EC2 instances and their costs"
- "Analyze S3 storage costs by bucket"
- "Compare RDS pricing options"
- "Generate a cost optimization report"
- "Monitor Lambda function performance"

---
**Backend Status**: {health_status.get('status', 'unknown').title()}
**Available Tools**: {health_status.get('available_tools', 0)}
**Active Servers**: {health_status.get('active_servers', 0)}

Ready to optimize your AWS infrastructure! 💪
"""
    
    await cl.Message(content=welcome_msg).send()
    
    # Set up action buttons for quick access
    actions = [
        cl.Action(name="dashboard", value="show_dashboard", label="📊 Dashboard"),
        cl.Action(name="cost_analysis", value="cost_analysis", label="💰 Cost Analysis"),
        cl.Action(name="health_check", value="health_check", label="🔍 Health Check"),
        cl.Action(name="help", value="help", label="❓ Help")
    ]
    
    await cl.Message(content="Choose a quick action:", actions=actions).send()

@cl.on_message
async def main(message: cl.Message):
    """Handle incoming messages from users with enhanced UX features."""
    user_id = cl.user_session.get("user_id")
    session_data = cl.user_session.get("session_data")

    # Update session activity
    session_manager.update_session_activity(user_id)

    # Check for special commands
    if message.content.startswith("/"):
        await handle_special_command(message.content, session_data["session_id"])
        return

    # Get command suggestions before processing
    suggestions = command_engine.get_suggestions(message.content)

    # Show enhanced loading indicator
    operation_id = f"msg_{int(time.time())}"
    loading_msg = await loading_manager.show_loading(
        operation_id,
        "Processing your request...",
        estimated_duration=10
    )

    try:
        # Update loading with progress
        await loading_manager.update_loading(operation_id, "Analyzing your request...", 20)

        # Send message to AWS Agent backend with error handling
        response = await safe_execute(
            aws_client.send_message,
            message.content,
            session_data["session_id"],
            error_handler=error_handler
        )

        # Check if safe_execute returned an error
        if isinstance(response, dict) and "error" in response:
            error_info = response["error"]
            error_message = format_error_for_user(error_info)

            # Add suggestions if available
            if suggestions:
                error_message += "\n\n💡 **Did you mean one of these?**\n"
                for suggestion in suggestions[:3]:
                    error_message += f"- {suggestion.command}\n"

            await loading_manager.fail_loading(operation_id, "Request failed")
            await cl.Message(content=error_message).send()
            return

        # Update loading progress
        await loading_manager.update_loading(operation_id, "Processing AWS response...", 60)

        if response.get("status") == "error":
            # Handle AWS Agent errors
            error_msg = f"❌ **AWS Agent Error**: {response.get('error', 'Unknown error occurred')}"

            if suggestions:
                error_msg += "\n\n💡 **Did you mean one of these?**\n"
                for suggestion in suggestions[:3]:
                    error_msg += f"- {suggestion.command}\n"

            await loading_manager.fail_loading(operation_id, "AWS Agent error")
            await cl.Message(content=error_msg).send()
            return

        # Update loading progress
        await loading_manager.update_loading(operation_id, "Formatting response...", 80)

        # Process and format the response
        formatted_response = await format_aws_response(response)

        # Complete loading
        await loading_manager.complete_loading(operation_id, "Request completed successfully")

        # Send the formatted response
        response_msg = await cl.Message(content=formatted_response).send()

        # Add follow-up suggestions if relevant
        if suggestions:
            # Optimize for mobile if needed
            follow_up_actions = []
            for suggestion in suggestions[:3]:
                follow_up_actions.append(
                    cl.Action(
                        name=f"suggest_{suggestion.command.replace(' ', '_')}",
                        value=suggestion.examples[0] if suggestion.examples else suggestion.command,
                        label=f"💡 {suggestion.command.title()}"
                    )
                )

            # Optimize actions for mobile
            optimized_actions = ResponsiveDesignHelper.optimize_action_buttons(follow_up_actions)

            if optimized_actions:
                await cl.Message(
                    content="**Related actions you might want to try:**",
                    actions=optimized_actions
                ).send()

        # Add to conversation history
        session_data["conversation_history"].append({
            "timestamp": datetime.now().isoformat(),
            "user_message": message.content,
            "assistant_response": response.get("response", ""),
            "tools_used": response.get("tools_used", []),
            "suggestions_shown": [s.command for s in suggestions],
            "success": True
        })

    except Exception as e:
        # Comprehensive error handling
        error_info = await error_handler.handle_error(e, {
            "user_message": message.content,
            "session_id": session_data["session_id"]
        })

        error_message = format_error_for_user(error_info)

        # Add suggestions for recovery
        if suggestions:
            error_message += "\n\n💡 **Try these alternatives:**\n"
            for suggestion in suggestions[:2]:
                error_message += f"- {suggestion.examples[0] if suggestion.examples else suggestion.command}\n"

        await loading_manager.fail_loading(operation_id, "System error occurred")
        await cl.Message(content=error_message).send()

        # Add failed interaction to history
        session_data["conversation_history"].append({
            "timestamp": datetime.now().isoformat(),
            "user_message": message.content,
            "assistant_response": error_message,
            "tools_used": [],
            "error_id": error_info.error_id,
            "success": False
        })

async def format_aws_response(response: Dict) -> str:
    """Format AWS Agent response for better display."""
    content = response.get("response", "")
    tools_used = response.get("tools_used", [])
    
    # Add tool usage information if available
    if tools_used:
        tool_info = "\n\n---\n**🔧 Tools Used**: " + ", ".join(tools_used)
        content += tool_info
    
    # Add session information
    session_id = response.get("session_id", "")
    if session_id:
        content += f"\n\n*Session ID: {session_id[:8]}...*"
    
    return content

@cl.on_action
async def on_action(action: cl.Action):
    """Handle action button clicks."""
    user_id = cl.user_session.get("user_id")
    session_data = cl.user_session.get("session_data")

    if action.value == "show_dashboard":
        await show_monitoring_dashboard(session_data["session_id"])
    elif action.value == "cost_analysis":
        await show_cost_analysis(session_data["session_id"])
    elif action.value == "health_check":
        await show_health_status()
    elif action.value == "help":
        await show_help_documentation()
    elif action.value == "export_report":
        await handle_export_request(session_data["session_id"])
    elif action.value == "security_dashboard":
        await show_security_dashboard(session_data["session_id"])

async def show_monitoring_dashboard(session_id: str):
    """Display comprehensive AWS monitoring dashboard."""
    try:
        # Show loading message
        loading_msg = await cl.Message(content="🔄 Loading monitoring dashboard...").send()

        # Get infrastructure overview
        infra_overview = await monitoring_dashboard.show_infrastructure_overview(session_id)

        # Update the loading message with actual data
        await loading_msg.update(content=infra_overview)

        # Add action buttons for detailed views
        actions = [
            cl.Action(name="performance_metrics", value="performance_metrics", label="📊 Performance"),
            cl.Action(name="security_dashboard", value="security_dashboard", label="🔒 Security"),
            cl.Action(name="export_report", value="export_report", label="📥 Export")
        ]

        await cl.Message(content="Choose a detailed view:", actions=actions).send()

    except Exception as e:
        await cl.Message(content=f"❌ Failed to load monitoring dashboard: {str(e)}").send()

async def show_cost_analysis(session_id: str):
    """Display comprehensive cost analysis dashboard."""
    try:
        # Show loading message
        loading_msg = await cl.Message(content="💰 Loading cost analysis...").send()

        # Get cost dashboard
        cost_dashboard = await monitoring_dashboard.show_cost_dashboard(session_id)

        # Update with actual data
        await loading_msg.update(content=cost_dashboard)

        # Add time period selection actions
        actions = [
            cl.Action(name="cost_7d", value="cost_7d", label="📅 Last 7 Days"),
            cl.Action(name="cost_30d", value="cost_30d", label="📅 Last 30 Days"),
            cl.Action(name="cost_90d", value="cost_90d", label="📅 Last 90 Days"),
            cl.Action(name="cost_optimization", value="cost_optimization", label="🎯 Optimization")
        ]

        await cl.Message(content="Select time period or view optimization opportunities:", actions=actions).send()

    except Exception as e:
        await cl.Message(content=f"❌ Failed to load cost analysis: {str(e)}").send()

async def show_health_status():
    """Display system health status."""
    try:
        health_data = await aws_client.health_check()
        
        status_emoji = "🟢" if health_data.get("status") == "healthy" else "🔴"
        
        health_content = f"""# {status_emoji} System Health Status

## 🔧 **Backend Service**
- **Status**: {health_data.get('status', 'unknown').title()}
- **Initialized**: {'✅' if health_data.get('initialized') else '❌'}
- **Available Tools**: {health_data.get('available_tools', 0)}
- **Active Servers**: {health_data.get('active_servers', 0)}

## 📊 **Session Information**
- **Session ID**: {cl.user_session.get('session_data', {}).get('session_id', 'N/A')[:8]}...
- **Messages Sent**: {cl.user_session.get('session_data', {}).get('message_count', 0)}
- **Session Duration**: {datetime.now() - cl.user_session.get('session_data', {}).get('created_at', datetime.now())}

## 🌐 **Connectivity**
- **AWS Agent URL**: {AWS_AGENT_URL}
- **API Timeout**: {API_TIMEOUT}s
- **Max File Size**: {MAX_FILE_SIZE / 1024 / 1024:.1f}MB

All systems operational! 🚀
"""
        
        await cl.Message(content=health_content).send()
        
    except Exception as e:
        await cl.Message(content=f"❌ Failed to retrieve health status: {e}").send()

async def show_help_documentation(topic: str = None):
    """Display comprehensive help documentation."""
    try:
        # Get help content from help system
        help_content = help_system.get_help(topic)

        # Add navigation breadcrumbs
        breadcrumbs = navigation_helper.get_breadcrumbs()
        full_content = f"**Navigation**: {breadcrumbs}\n\n{help_content}"

        await cl.Message(content=full_content).send()

        # Add help navigation actions
        help_actions = [
            cl.Action(name="help_commands", value="help_commands", label="📋 Commands"),
            cl.Action(name="help_cost", value="help_cost_analysis", label="💰 Cost Analysis"),
            cl.Action(name="help_search", value="help_search", label="🔍 Search Help"),
            cl.Action(name="help_examples", value="help_examples", label="💡 Examples")
        ]

        await cl.Message(content="**Browse help topics:**", actions=help_actions).send()

    except Exception as e:
        await cl.Message(content=f"❌ Failed to load help: {str(e)}").send()

async def handle_special_command(command: str, session_id: str):
    """Handle special commands starting with /."""
    command = command.lower().strip()

    if command == "/help":
        await show_help_documentation()
    elif command.startswith("/search "):
        query = command[8:]  # Remove "/search "
        await handle_search_command(query, session_id)
    elif command == "/history":
        await show_conversation_history(session_id)
    elif command == "/suggestions":
        await show_command_suggestions()
    elif command == "/navigation":
        await show_navigation_options()
    elif command == "/clear":
        await clear_conversation_history(session_id)
    else:
        await cl.Message(
            content=f"❓ Unknown command: {command}\n\nAvailable commands:\n"
                   "- `/help` - Show help documentation\n"
                   "- `/search <query>` - Search resources and documentation\n"
                   "- `/history` - Show conversation history\n"
                   "- `/suggestions` - Show command suggestions\n"
                   "- `/navigation` - Show navigation options\n"
                   "- `/clear` - Clear conversation history"
        ).send()

async def handle_search_command(query: str, session_id: str):
    """Handle search commands."""
    try:
        if not query.strip():
            await cl.Message(content="❓ Please provide a search query. Example: `/search ec2 instances`").send()
            return

        # Show searching indicator
        search_msg = await cl.Message(content=f"🔍 Searching for: {query}...").send()

        # Perform search
        results = search_engine.search(query)

        if not results:
            await search_msg.update(content=f"🔍 No results found for: {query}")
            return

        # Format search results
        search_content = f"# 🔍 Search Results for: {query}\n\n"

        # Group results by type
        resource_results = [r for r in results if r.get('type') == 'resource']
        doc_results = [r for r in results if r.get('type') == 'documentation']
        command_results = [r for r in results if r.get('type') == 'command']

        if resource_results:
            search_content += "## 🏗️ AWS Resources\n"
            for result in resource_results[:5]:
                search_content += f"- **{result['title']}** ({result['category']})\n"
                search_content += f"  {result['description']}\n"
                if 'id' in result:
                    search_content += f"  ID: `{result['id']}`\n"
                search_content += "\n"

        if command_results:
            search_content += "## 🔧 Available Commands\n"
            for result in command_results[:3]:
                search_content += f"- **{result['title']}**\n"
                search_content += f"  {result['description']}\n"
                if 'examples' in result and result['examples']:
                    search_content += f"  Example: `{result['examples'][0]}`\n"
                search_content += "\n"

        if doc_results:
            search_content += "## 📚 Documentation\n"
            for result in doc_results[:3]:
                search_content += f"- **{result['title']}**\n"
                search_content += f"  {result['description']}\n\n"

        await search_msg.update(content=search_content)

        # Add search suggestions
        suggestions = search_engine.get_search_suggestions(query)
        if suggestions:
            suggestion_actions = []
            for suggestion in suggestions[:3]:
                suggestion_actions.append(
                    cl.Action(
                        name=f"search_{suggestion.replace(' ', '_')}",
                        value=f"/search {suggestion}",
                        label=f"🔍 {suggestion}"
                    )
                )

            await cl.Message(
                content="**Related searches:**",
                actions=suggestion_actions
            ).send()

    except Exception as e:
        await cl.Message(content=f"❌ Search failed: {str(e)}").send()

async def show_conversation_history(session_id: str):
    """Show conversation history for the session."""
    try:
        session_data = cl.user_session.get("session_data", {})
        history = session_data.get("conversation_history", [])

        if not history:
            await cl.Message(content="📝 No conversation history available for this session.").send()
            return

        history_content = "# 📝 Conversation History\n\n"

        # Show last 10 interactions
        recent_history = history[-10:]

        for i, interaction in enumerate(recent_history, 1):
            timestamp = interaction.get("timestamp", "Unknown")
            user_msg = interaction.get("user_message", "")
            tools_used = interaction.get("tools_used", [])

            history_content += f"## {i}. {timestamp}\n"
            history_content += f"**You**: {user_msg[:100]}{'...' if len(user_msg) > 100 else ''}\n"

            if tools_used:
                history_content += f"**Tools Used**: {', '.join(tools_used)}\n"

            history_content += "\n"

        # Add session statistics
        total_messages = len(history)
        total_tools = sum(len(h.get("tools_used", [])) for h in history)

        history_content += f"---\n**Session Statistics**:\n"
        history_content += f"- Total Messages: {total_messages}\n"
        history_content += f"- Tools Used: {total_tools}\n"
        history_content += f"- Session Duration: {datetime.now() - session_data.get('created_at', datetime.now())}\n"

        await cl.Message(content=history_content).send()

    except Exception as e:
        await cl.Message(content=f"❌ Failed to load conversation history: {str(e)}").send()

async def show_command_suggestions():
    """Show available command suggestions."""
    try:
        suggestions_content = "# 💡 Command Suggestions\n\n"

        # Get all commands grouped by category
        all_commands = command_engine.commands

        # Group by category
        categories = {}
        for cmd in all_commands:
            category = cmd.category.value
            if category not in categories:
                categories[category] = []
            categories[category].append(cmd)

        # Display by category
        category_emojis = {
            "cost_analysis": "💰",
            "resource_management": "🏗️",
            "monitoring": "📊",
            "security": "🔒",
            "optimization": "🎯",
            "reporting": "📋"
        }

        for category, commands in categories.items():
            emoji = category_emojis.get(category, "🔧")
            category_title = category.replace("_", " ").title()

            suggestions_content += f"## {emoji} {category_title}\n\n"

            for cmd in commands:
                suggestions_content += f"### {cmd.command}\n"
                suggestions_content += f"{cmd.description}\n\n"

                if cmd.examples:
                    suggestions_content += "**Examples:**\n"
                    for example in cmd.examples[:2]:
                        suggestions_content += f"- `{example}`\n"
                    suggestions_content += "\n"

        await cl.Message(content=suggestions_content).send()

    except Exception as e:
        await cl.Message(content=f"❌ Failed to load command suggestions: {str(e)}").send()

async def show_navigation_options():
    """Show navigation options and current context."""
    try:
        nav_content = "# 🧭 Navigation\n\n"

        # Current context
        breadcrumbs = navigation_helper.get_breadcrumbs()
        nav_content += f"**Current Location**: {breadcrumbs}\n\n"

        # Available actions
        nav_actions = navigation_helper.get_navigation_actions()

        if nav_actions:
            nav_content += "## Available Actions\n\n"
            for action in nav_actions:
                nav_content += f"- {action['label']}\n"

        await cl.Message(content=nav_content).send()

        # Convert to Chainlit actions
        cl_actions = []
        for action in nav_actions:
            cl_actions.append(
                cl.Action(
                    name=action['name'],
                    value=action['value'],
                    label=action['label']
                )
            )

        if cl_actions:
            await cl.Message(content="**Quick Navigation:**", actions=cl_actions).send()

    except Exception as e:
        await cl.Message(content=f"❌ Failed to load navigation options: {str(e)}").send()

async def clear_conversation_history(session_id: str):
    """Clear conversation history for the session."""
    try:
        session_data = cl.user_session.get("session_data", {})
        session_data["conversation_history"] = []
        cl.user_session.set("session_data", session_data)

        await cl.Message(content="🧹 Conversation history cleared successfully!").send()

    except Exception as e:
        await cl.Message(content=f"❌ Failed to clear conversation history: {str(e)}").send()

@cl.on_file_upload
async def on_file_upload(file: cl.File):
    """Handle file uploads for AWS configurations and scripts."""
    try:
        # Validate file size
        if file.size > MAX_FILE_SIZE:
            await cl.Message(
                content=f"❌ File too large. Maximum size is {MAX_FILE_SIZE / 1024 / 1024:.1f}MB"
            ).send()
            return

        # Process different file types
        file_extension = Path(file.name).suffix.lower()

        if file_extension in ['.json', '.yaml', '.yml']:
            # AWS configuration files
            content = file.content.decode('utf-8')
            await process_aws_config_file(file.name, content)
        elif file_extension in ['.py', '.sh', '.ps1']:
            # Scripts
            content = file.content.decode('utf-8')
            await process_script_file(file.name, content)
        elif file_extension in ['.csv', '.xlsx']:
            # Data files
            await process_data_file(file)
        else:
            await cl.Message(
                content=f"📄 File '{file.name}' uploaded successfully. Supported formats: JSON, YAML, Python, Shell scripts, CSV, Excel."
            ).send()

    except Exception as e:
        logger.error(f"File upload error: {e}")
        await cl.Message(
            content=f"❌ Failed to process file: {str(e)}"
        ).send()

async def process_aws_config_file(filename: str, content: str):
    """Process AWS configuration files with enhanced analysis."""
    try:
        # Create a temporary file object for processing
        temp_file = type('TempFile', (), {
            'name': filename,
            'content': content.encode('utf-8'),
            'size': len(content.encode('utf-8'))
        })()

        # Use the file processor for detailed analysis
        analysis = await file_processor.process_file(temp_file)

        # Format the analysis results
        file_icon = get_file_icon(filename)
        formatted_analysis = f"""# {file_icon} Configuration File Analysis: {filename}

## 🔍 **File Overview**
- **Type**: {analysis.file_type}
- **Size**: {format_file_size(analysis.size_bytes)}
- **Lines**: {analysis.line_count:,}

## 🔧 **AWS Resources Detected**
{', '.join(analysis.aws_resources) if analysis.aws_resources else 'None detected'}

## 🚨 **Security Issues**
"""

        if analysis.security_issues:
            for issue in analysis.security_issues:
                severity_emoji = "🔴" if issue['severity'] == 'high' else "🟡"
                formatted_analysis += f"- {severity_emoji} **{issue['type'].replace('_', ' ').title()}**: {issue['pattern']}\n"
        else:
            formatted_analysis += "✅ No security issues detected\n"

        formatted_analysis += "\n## 💡 **Recommendations**\n"
        if analysis.recommendations:
            for rec in analysis.recommendations:
                formatted_analysis += f"- {rec}\n"
        else:
            formatted_analysis += "- Configuration looks good!\n"

        # Add metadata if available
        if analysis.metadata:
            formatted_analysis += "\n## 📊 **Additional Details**\n"
            for key, value in analysis.metadata.items():
                formatted_analysis += f"- **{key.replace('_', ' ').title()}**: {value}\n"

        await cl.Message(content=formatted_analysis).send()

        # Offer additional actions
        actions = [
            cl.Action(name="optimize_config", value=f"optimize_config_{filename}", label="🎯 Optimize"),
            cl.Action(name="security_scan", value=f"security_scan_{filename}", label="🔒 Security Scan"),
            cl.Action(name="cost_estimate", value=f"cost_estimate_{filename}", label="💰 Cost Estimate")
        ]

        await cl.Message(content="What would you like to do with this configuration?", actions=actions).send()

    except Exception as e:
        await cl.Message(
            content=f"❌ Failed to analyze configuration file: {str(e)}"
        ).send()

async def process_script_file(filename: str, content: str):
    """Process script files."""
    lines = content.split('\n')

    analysis = f"""# 📜 Script Analysis: {filename}

## 🔍 **Script Overview**
- **Type**: {'Python' if filename.endswith('.py') else 'Shell'} Script
- **Lines**: {len(lines)}
- **Size**: {len(content)} characters

## 🔧 **Quick Analysis**
- **AWS CLI Commands**: {content.count('aws ')} found
- **Boto3 Imports**: {'✅' if 'boto3' in content else '❌'}
- **Error Handling**: {'✅' if 'try:' in content or 'except' in content else '❌'}

**💡 Tip**: I can help optimize this script or suggest AWS best practices!
"""

    await cl.Message(content=analysis).send()

async def process_data_file(file: cl.File):
    """Process data files like CSV or Excel."""
    try:
        if file.name.endswith('.csv'):
            df = pd.read_csv(file.path)
        else:
            df = pd.read_excel(file.path)

        analysis = f"""# 📊 Data File Analysis: {file.name}

## 🔍 **Dataset Overview**
- **Rows**: {len(df)}
- **Columns**: {len(df.columns)}
- **Size**: {file.size / 1024:.1f} KB

## 📈 **Column Information**
{df.dtypes.to_string()}

## 📋 **Sample Data**
{df.head().to_string()}

**💡 Tip**: I can help analyze this data for AWS cost patterns or resource usage!
"""

        await cl.Message(content=analysis).send()

    except Exception as e:
        await cl.Message(
            content=f"❌ Failed to process data file: {str(e)}"
        ).send()

@cl.on_stop
async def on_stop():
    """Handle session cleanup when user disconnects."""
    user_id = cl.user_session.get("user_id")
    if user_id and user_id in session_manager.sessions:
        logger.info(f"Session ended for user {user_id}")
        # Could implement session persistence here if needed

@cl.on_settings_update
async def setup_agent(settings):
    """Handle settings updates."""
    print("Settings updated:", settings)

async def show_security_dashboard(session_id: str):
    """Display security and compliance dashboard."""
    try:
        loading_msg = await cl.Message(content="🔒 Loading security dashboard...").send()

        security_dashboard = await monitoring_dashboard.show_security_dashboard(session_id)
        await loading_msg.update(content=security_dashboard)

    except Exception as e:
        await cl.Message(content=f"❌ Failed to load security dashboard: {str(e)}").send()

async def show_performance_metrics(session_id: str):
    """Display performance metrics dashboard."""
    try:
        loading_msg = await cl.Message(content="📊 Loading performance metrics...").send()

        performance_metrics = await monitoring_dashboard.show_performance_metrics(session_id)
        await loading_msg.update(content=performance_metrics)

    except Exception as e:
        await cl.Message(content=f"❌ Failed to load performance metrics: {str(e)}").send()

async def handle_export_request(session_id: str):
    """Handle export report requests."""
    try:
        # Show export options
        actions = [
            cl.Action(name="export_cost_json", value="export_cost_json", label="💰 Cost Report (JSON)"),
            cl.Action(name="export_cost_excel", value="export_cost_excel", label="💰 Cost Report (Excel)"),
            cl.Action(name="export_infra_html", value="export_infra_html", label="🏗️ Infrastructure (HTML)"),
            cl.Action(name="export_security_json", value="export_security_json", label="🔒 Security Report (JSON)"),
            cl.Action(name="export_full_backup", value="export_full_backup", label="📦 Full Backup (ZIP)")
        ]

        await cl.Message(content="Select the type of report to export:", actions=actions).send()

    except Exception as e:
        await cl.Message(content=f"❌ Failed to prepare export options: {str(e)}").send()

async def process_export_action(action_value: str, session_id: str):
    """Process specific export actions."""
    try:
        loading_msg = await cl.Message(content="📥 Preparing export...").send()

        if action_value == "export_cost_json":
            # Get cost data and export as JSON
            cost_data = await monitoring_dashboard._get_cost_data(session_id, "30d")
            export_data = await report_exporter.export_cost_report(cost_data, "json")

            # Create downloadable file
            await cl.Message(
                content="💰 Cost report exported successfully!",
                elements=[cl.File(
                    name="cost_report.json",
                    content=export_data,
                    display="inline"
                )]
            ).send()

        elif action_value == "export_cost_excel":
            cost_data = await monitoring_dashboard._get_cost_data(session_id, "30d")
            export_data = await report_exporter.export_cost_report(cost_data, "xlsx")

            await cl.Message(
                content="💰 Cost report exported as Excel!",
                elements=[cl.File(
                    name="cost_report.xlsx",
                    content=export_data,
                    display="inline"
                )]
            ).send()

        elif action_value == "export_infra_html":
            infra_data = await monitoring_dashboard._get_infrastructure_data(session_id)
            export_data = await report_exporter.export_infrastructure_report(infra_data, "html")

            await cl.Message(
                content="🏗️ Infrastructure report exported as HTML!",
                elements=[cl.File(
                    name="infrastructure_report.html",
                    content=export_data,
                    display="inline"
                )]
            ).send()

        elif action_value == "export_security_json":
            security_data = await monitoring_dashboard._get_security_data(session_id)
            export_data = await report_exporter.export_security_report(security_data, "json")

            await cl.Message(
                content="🔒 Security report exported successfully!",
                elements=[cl.File(
                    name="security_report.json",
                    content=export_data,
                    display="inline"
                )]
            ).send()

        elif action_value == "export_full_backup":
            # Collect all data
            all_data = {
                "cost_data": await monitoring_dashboard._get_cost_data(session_id, "30d"),
                "infrastructure_data": await monitoring_dashboard._get_infrastructure_data(session_id),
                "security_data": await monitoring_dashboard._get_security_data(session_id),
                "performance_data": await monitoring_dashboard._get_performance_data(session_id)
            }

            export_data = await report_exporter.create_backup_archive(all_data)

            await cl.Message(
                content="📦 Full backup created successfully!",
                elements=[cl.File(
                    name="aws_management_backup.zip",
                    content=export_data,
                    display="inline"
                )]
            ).send()

        await loading_msg.update(content="✅ Export completed successfully!")

    except Exception as e:
        await cl.Message(content=f"❌ Export failed: {str(e)}").send()

if __name__ == "__main__":
    # This will be handled by Chainlit's CLI
    pass
